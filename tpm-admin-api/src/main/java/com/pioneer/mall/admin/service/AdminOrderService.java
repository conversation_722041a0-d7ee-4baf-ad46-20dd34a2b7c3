package com.pioneer.mall.admin.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyV3Response;
import com.github.binarywang.wxpay.bean.notify.WxPayPartnerRefundNotifyV3Result;
import com.github.binarywang.wxpay.bean.request.WxPayRefundV3Request;
import com.github.binarywang.wxpay.bean.result.BaseWxPayResult;
import com.github.binarywang.wxpay.bean.result.WxPayRefundV3Result;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.pagehelper.PageInfo;
import com.pioneer.mall.admin.enums.Constants;
import com.pioneer.mall.admin.util.AdminResponseUtil;
import com.pioneer.mall.admin.vo.req.OrderUpdateStatusReqVo;
import com.pioneer.mall.core.express.ExpressService;
import com.pioneer.mall.core.judanke.JdkOrderService;
import com.pioneer.mall.core.notify.NotifyService;
import com.pioneer.mall.core.system.SystemConfig;
import com.pioneer.mall.core.type.OrderFreightTypeEnum;
import com.pioneer.mall.core.util.*;
import com.pioneer.mall.core.vo.TpmOrderGoodsResVo;
import com.pioneer.mall.db.dao.TpmBalanceMapper;
import com.pioneer.mall.db.domain.*;
import com.pioneer.mall.db.dto.TpmPointDto;
import com.pioneer.mall.db.enums.OrderPayTypeEnums;
import com.pioneer.mall.db.enums.StatusEnum;
import com.pioneer.mall.db.enums.TpmBusinessTypeEnums;
import com.pioneer.mall.db.enums.WxRefundOrderStatusEnums;
import com.pioneer.mall.db.service.*;
import com.pioneer.mall.db.util.OrderUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.pioneer.mall.admin.util.AdminResponseCode.*;

@Slf4j
@Service
public class AdminOrderService {
    private static final Logger logger = LoggerFactory.getLogger(AdminOrderService.class);

    @Autowired
    private TpmOrderGoodsService orderGoodsService;
    @Autowired
    private TpmOrderService orderService;
    @Autowired
    private TpmGoodsProductService productService;
    @Autowired
    private TpmUserService userService;
    @Autowired
    private TpmCommentService commentService;
    @Autowired
    private ExpressService expressService;
    @Autowired
    private WxPayService wxPayService;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private TpmCouponUserService tpmCouponUserService;
    @Autowired
    private TpmCouponService tpmCouponService;

    @Resource
    private TpmDeliveryRecordService deliveryRecordService;
    @Autowired
    private TpmGoodsService tpmGoodsService;
    @Autowired
    private AdminDataAuthService adminDataAuthService;
    @Autowired
    private TpmPointService pointService;
    @Autowired
    private TpmBalanceMapper tpmBalanceMapper;
    @Autowired
    private JdkOrderService jdkOrderService;
    @Autowired
    private TpmCouponCodeUseLogService tpmCouponCodeUseLogService;
    @Autowired
    private TpmWaybillRouteService tpmWaybillRouteService;
    @Autowired
    private SfExpressUtils sfExpressUtils;
    @Autowired
    private WxUploadShippingInfoUtils wxUploadShippingInfoUtils;

    public Object list(Integer userId, String orderSn, List<Short> orderStatusArray, Integer page, Integer limit,
                       String sort, String order, Integer businessType, Integer freightType) {

        // 需要区分数据权限，如果属于品牌商管理员，则需要获取当前用户管理品牌店铺
        List<Integer> brandIds = null;
        if (adminDataAuthService.isBrandManager()) {
            brandIds = adminDataAuthService.getBrandIds();
            logger.info("运营商管理角色操作，需控制数据权限，brandIds:{}", JSONObject.toJSONString(brandIds));

            if (brandIds == null || brandIds.size() == 0) {//如果尚未管理任何入驻店铺，则返回空数据
                Map<String, Object> data = new HashMap<>();
                data.put("total", 0L);
                data.put("items", null);

                logger.info("【请求结束】商场管理->订单管理->查询,响应结果:{}", JSONObject.toJSONString(data));
                return ResponseUtil.ok(data);
            }
        }
        List<TpmOrder> orderList = null;
        long total = 0L;
        if (brandIds == null || brandIds.size() == 0) {
            orderList = orderService.querySelective(userId, orderSn, orderStatusArray, page, limit, sort, order, businessType, freightType);
            total = PageInfo.of(orderList).getTotal();
        } else {
            orderList = orderService.queryBrandSelective(brandIds, userId, orderSn, orderStatusArray, page, limit, sort, order, businessType, freightType);
            total = PageInfo.of(orderList).getTotal();
        }
        orderList.forEach(or -> {

            if (StringUtils.isEmpty(or.getShipSn())) {
                or.setDeliveryStatus("");
            }

            if (Objects.equals(or.getFreightType(), (byte) 1) || Objects.equals(or.getFreightType(), (byte) 2)) {
                Boolean mealPickupStatus = or.getMealPickupStatus();
                if (Objects.equals(mealPickupStatus, true)) {
                    or.setDeliveryStatus("已核销");
                } else {
                    or.setDeliveryStatus("未核销");
                }
            } else if (Objects.equals(or.getFreightType(), (byte) 0)) {
                String deliveryStatus = or.getDeliveryStatus();
                try {
                    if (StringUtils.isEmpty(deliveryStatus)) {
                        or.setDeliveryStatus("");
                    } else {
                        switch (Integer.parseInt(deliveryStatus)) {
                            case 1:
                                or.setDeliveryStatus("接单");
                                break;
                            case 2:
                                or.setDeliveryStatus("到店");
                                break;
                            case 3:
                                or.setDeliveryStatus("配送");
                                break;
                            case 4:
                                or.setDeliveryStatus("完成");
                                break;
                            case 5:
                                or.setDeliveryStatus("异常");
                                break;
                            case 6:
                                or.setDeliveryStatus("发单失败");
                                break;
                            case 7:
                                or.setDeliveryStatus("运力取消");
                                break;
                            default:
                                or.setDeliveryStatus("未知状态");
                                break;
                        }
                    }
                } catch (Exception e) {
                    log.error("订单状态转换异常:{}", e.getMessage(), e);
                }
            }
        });
        Map<String, Object> data = new HashMap<>();
        data.put("total", total);
        data.put("items", orderList);

        logger.info("【请求结束】商场管理->订单管理->查询,响应结果:{}", JSONObject.toJSONString(data));
        return ResponseUtil.ok(data);
    }

    public Object detail(Integer id) {
        TpmOrder order = orderService.findById(id);
        List<TpmOrderGoods> orderGoods = orderGoodsService.queryByOid(id);
        List<TpmOrderGoodsResVo> goodsResVoList = GoodsSpecificationUtil.getOrderGoodsResVoList(orderGoods);
        List<TpmDeliveryRecord> deliveryRecords = deliveryRecordService.findByTpmOrderId(id);
        UserVo user = userService.findUserVoById(order.getUserId());
        if (Objects.equals(order.getFreightType(), (byte) 1) || Objects.equals(order.getFreightType(), (byte) 2)) {
            Boolean mealPickupStatus = order.getMealPickupStatus();
            if (Objects.equals(mealPickupStatus, true)) {
                order.setDeliveryStatus("已核销");
            } else {
                order.setDeliveryStatus("未核销");
            }
        } else if (Objects.equals(order.getFreightType(), (byte) 0)) {
            String deliveryStatus = order.getDeliveryStatus();
            try {
                switch (Integer.parseInt(deliveryStatus)) {
                    case 1:
                        order.setDeliveryStatus("接单");
                        break;
                    case 2:
                        order.setDeliveryStatus("到店");
                        break;
                    case 3:
                        order.setDeliveryStatus("配送");
                        break;
                    case 4:
                        order.setDeliveryStatus("完成");
                        break;
                    case 5:
                        order.setDeliveryStatus("异常");
                        break;
                    case 6:
                        order.setDeliveryStatus("发单失败");
                        break;
                    case 7:
                        order.setDeliveryStatus("运力取消");
                        break;
                    default:
                        order.setDeliveryStatus("未知状态");
                        break;
                }
            } catch (Exception e) {
                log.error("订单状态转换异常:{}", e.getMessage(), e);
            }
        }

        // 商城轨迹日志
        List<TpmWaybillRoute> tpmWaybillRoutes = tpmWaybillRouteService.findByTpmOrderId(id);

        Map<String, Object> data = new HashMap<>();
        data.put("order", order);
        data.put("orderGoods", goodsResVoList);
        data.put("user", user);
        data.put("delivery", deliveryRecords);
        data.put("mallTraceLogs", tpmWaybillRoutes);

        logger.info("【请求结束】商场管理->订单管理->详情,响应结果:{}", JSONObject.toJSONString(data));
        return ResponseUtil.ok(data);
    }

    private static void notifyWx(WxPayPartnerRefundNotifyV3Result wxPayPartnerRefundNotifyV3Result, WxPayPartnerRefundNotifyV3Result.DecryptNotifyResult result, String orderSn, WxRefundOrderStatusEnums refundOrderStatusEnums) {
        //通知的类型:REFUND.SUCCESS：退款成功通知
        //REFUND.ABNORMAL：退款异常通知
        //REFUND.CLOSED：退款关闭通知
        String eventType = wxPayPartnerRefundNotifyV3Result.getRawData().getEventType();
        if (Objects.equals(eventType, "REFUND.SUCCESS")) {
            eventType = "退款成功通知";
        } else if (Objects.equals(eventType, "REFUND.ABNORMAL")) {
            eventType = "退款异常通知";
        } else if (Objects.equals(eventType, "REFUND.CLOSED")) {
            eventType = "退款关闭通知";
        }
        WxPayPartnerRefundNotifyV3Result.Amount amount = result.getAmount();
        String totalFee = BaseWxPayResult.fenToYuan(amount.getTotal());
        String refund = BaseWxPayResult.fenToYuan(amount.getPayerRefund());
        String userReceivedAccount = result.getUserReceivedAccount();
        String notifyMessage = eventType + " 订单" + orderSn + "退款状态为:" + refundOrderStatusEnums.getDesc() + ",订单总金额:" + totalFee + ",退款金额:" + refund + ",入账方式:" + userReceivedAccount;
        WechatNotifyUtils.wechatNotifyMd(SystemConfig.getWechatNotifyUrl(), null, notifyMessage);
    }

    /**
     * 订单退款
     * <p>
     * 1. 检测当前订单是否能够退款; 2. 微信退款操作; 3. 设置订单退款确认状态； 4. 订单商品库存回库。
     * <p>
     * TODO 虽然接入了微信退款API，但是从安全角度考虑，建议开发者删除这里微信退款代码，采用以下两步走步骤： 1.
     * 管理员登录微信官方支付平台点击退款操作进行退款 2. 管理员登录Dts管理后台点击退款操作进行订单状态修改和商品库存回库
     *
     * @param body 订单信息，{ orderId：xxx }
     * @return 订单退款操作结果
     */
    @Transactional
    public Object refund(String body) {
        Integer orderId = JacksonUtil.parseInteger(body, "orderId");
        String refundMoney = JacksonUtil.parseString(body, "refundMoney");
        if (orderId == null) {
            return ResponseUtil.badArgument();
        }
        if (StringUtils.isEmpty(refundMoney)) {
            return ResponseUtil.badArgument();
        }

        TpmOrder order = orderService.findById(orderId);
        if (order == null) {
            return ResponseUtil.badArgument();
        }

        if (order.getActualPrice().compareTo(new BigDecimal(refundMoney)) != 0) {
            return ResponseUtil.badArgumentValue();
        }

        // 如果订单不是退款状态，则不能退款
        if (!order.getOrderStatus().equals(OrderUtil.STATUS_REFUND)) {
            logger.info("商场管理->订单管理->订单退款失败:{}", ORDER_REFUND_FAILED.desc());
            return AdminResponseUtil.fail(ORDER_REFUND_FAILED);
        }


        if (Objects.nonNull(order.getPayType()) && order.getPayType().equals(OrderPayTypeEnums.BALANCE.getCode())) {
            logger.info("余额支付订单退款，余额回退 userId {}, actualPrice {}", order.getUserId(), order.getActualPrice());
            TpmUser tpmUser = userService.findById(order.getUserId());
            tpmUser.setBalance(tpmUser.getBalance().add(order.getActualPrice()));
            userService.updateById(tpmUser);
            TpmBalance tpmBalance = new TpmBalance();
            tpmBalance.setUserId(order.getUserId());
            tpmBalance.setMobile(tpmUser.getMobile());
            tpmBalance.setOrderSn(order.getOrderSn());
            tpmBalance.setAmount(order.getActualPrice());
            tpmBalance.setLeftBalance(tpmUser.getBalance());
            tpmBalance.setOperator(tpmUser.getNickname());
            tpmBalance.setStatus(StatusEnum.ENABLED.getKey());
            tpmBalance.setAddTime(LocalDateTime.now());
            tpmBalance.setUpdateTime(LocalDateTime.now());
            tpmBalance.setDeleted(false);
            tpmBalanceMapper.insert(tpmBalance);
            log.info("余额记录插入成功 balance={}", JSONObject.toJSONString(tpmBalance));
            refundPostProcess(order);
        } else if (Objects.equals(order.getPayType(), OrderPayTypeEnums.WECHAT.getCode())) {
//        为了账号安全，暂时屏蔽api退款
            if (Objects.equals(order.getRefundStatus(), WxRefundOrderStatusEnums.SUCCESS.getCode())) {
                return ResponseUtil.fail("订单已退款完成");
            }
            WxPayRefundV3Request wxPayRefundRequest = new WxPayRefundV3Request();
            if (Objects.equals(order.getBusinessType(), TpmBusinessTypeEnums.EXPRESS.getCode())) {
                List<TpmOrder> byPayId = orderService.findByPayId(order.getPayId());
                if (byPayId.size() > 1) {
                    TpmOrder cold = byPayId.stream()
                            .filter(b -> Objects.equals(b.getFreightType(), OrderFreightTypeEnum.COLD_CHAIN.getValue()))
                            .findFirst()
                            .orElse(null);
                    TpmOrder common = byPayId.stream()
                            .filter(b -> Objects.equals(b.getFreightType(), OrderFreightTypeEnum.COMMON.getValue()))
                            .findFirst()
                            .orElse(null);
                    if (Objects.isNull(cold) || Objects.isNull(common)) {
                        setBasicRefundInfo(wxPayRefundRequest, order.getOrderSn(), order.getActualPrice());
                    } else {
//                        String combinedOrderSn = cold.getOrderSn() + "-" + common.getOrderSn();
//                        wxPayRefundRequest.setOutTradeNo(combinedOrderSn);
                        wxPayRefundRequest.setTransactionId(order.getPayId());
                        wxPayRefundRequest.setOutRefundNo("refund_" + order.getOrderSn());
                        Integer coldTotalFee = cold.getActualPrice().multiply(new BigDecimal(100)).intValue();
                        Integer commonTotalFee = common.getActualPrice().multiply(new BigDecimal(100)).intValue();
                        setRefundAmount(wxPayRefundRequest, order.getActualPrice(), coldTotalFee + commonTotalFee);
                    }
                } else {
                    setBasicRefundInfo(wxPayRefundRequest, order.getOrderSn(), order.getActualPrice());
                }
            } else {
                setBasicRefundInfo(wxPayRefundRequest, order.getOrderSn(), order.getActualPrice());
            }
            wxPayRefundRequest.setNotifyUrl("https://www.7riverlight.com/demo/admin/order/wx/refundNotify");
            WxPayRefundV3Result wxPayRefundResult = null;
            try {
                logger.info("refund request={}", JSONObject.toJSONString(wxPayRefundRequest));
                wxPayRefundResult = wxPayService.refundV3(wxPayRefundRequest);
                order.setRefundStatus(wxPayRefundResult.getStatus());
                orderService.updateWithOptimisticLocker(order);
                logger.info("refund result ={}", JSONObject.toJSONString(wxPayRefundResult));
            } catch (WxPayException e) {
                logger.error("refund fail={}", e.getMessage(), e);
                return ResponseUtil.fail(ORDER_REFUND_FAILED.code(), "订单退款失败");
            }
            String status = wxPayRefundResult.getStatus();
            WxRefundOrderStatusEnums byCode = WxRefundOrderStatusEnums.getByCode(status);
            if (Objects.nonNull(byCode)) {
                String notifyMessage = "订单:" + order.getOrderSn() + "退款状态为:" + byCode.getDesc();
                WechatNotifyUtils.wechatNotifyMd(SystemConfig.getWechatNotifyUrl(), null, notifyMessage);
            }
        }
        // 退还下优惠券，如果需要的话
        tpmCouponUserService.giveBackCoupon(order.getOrderSn());

//        refundPostProcess(order);
        logger.info("【请求结束】商场管理->订单管理->订单退款,响应结果:{}", "成功!");
        return ResponseUtil.ok();
    }

    private static void setBasicRefundInfo(WxPayRefundV3Request request, String orderSn, BigDecimal actualPrice) {
        request.setOutTradeNo(orderSn);
        request.setOutRefundNo("refund_" + orderSn);
        setRefundAmount(request, actualPrice, actualPrice.multiply(new BigDecimal(100)).intValue());
    }

    private static void setRefundAmount(WxPayRefundV3Request request, BigDecimal refundPrice, Integer totalFee) {
        WxPayRefundV3Request.Amount amount = new WxPayRefundV3Request.Amount();
        amount.setRefund(refundPrice.multiply(new BigDecimal(100)).intValue());
        amount.setTotal(totalFee);
        amount.setCurrency("CNY");
        request.setAmount(amount);
    }

    private final ExecutorService executor = Executors.newFixedThreadPool(5);

    public Object shipSnAdd(Integer orderId, String shipSn) {

        if (orderId == null) {
            return ResponseUtil.fail("订单不能为空");
        }
        if (shipSn == null) {
            return ResponseUtil.fail("运单号不能为空");
        }
        TpmOrder tpmOrder = orderService.findById(orderId);
        if (Objects.isNull(tpmOrder)){
            return ResponseUtil.fail("订单不存在");
        }
        orderService.updateShipsn(orderId, shipSn);
        try {
            sfExpressUtils.registerRoute(tpmOrder.getOrderSn(),shipSn);
        } catch (Exception e) {
            logger.error("顺丰接口调用失败 error={}", e.getMessage(), e);
            WechatNotifyUtils.wechatNoteSend(SystemConfig.getWechatNotifyUrl(), null, "顺丰接口调用注册接口失败,单号:{}" + shipSn);
        }
        try {
            TpmOrder order = orderService.findById(orderId);
            //上传发货信息
            if (Objects.nonNull(order) && Objects.equals(order.getPayType(), OrderPayTypeEnums.WECHAT.getCode())) {
                logger.info("上传发货信息 orderSn={}",order.getOrderSn());
                wxUploadShippingInfoUtils.doUploadExpressShippingInfo(order.getId());
            }
        } catch (Exception e) {
            logger.error("上传发货信息失败 error={}", e.getMessage(), e);
        }
        return ResponseUtil.ok();
    }


    /**
     * 发货 1. 检测当前订单是否能够发货 2. 设置订单发货状态
     *
     * @param body 订单信息，{ orderId：xxx, shipSn: xxx, shipChannel: xxx }
     * @return 订单操作结果 成功则 { errno: 0, errmsg: '成功' } 失败则 { errno: XXX, errmsg: XXX }
     */
    public Object ship(String body) {
        Integer orderId = JacksonUtil.parseInteger(body, "orderId");
        String shipSn = JacksonUtil.parseString(body, "shipSn");
        String shipChannel = JacksonUtil.parseString(body, "shipChannel");
        if (orderId == null) {
            return ResponseUtil.badArgument();
        }

        TpmOrder order = orderService.findById(orderId);
        if (order == null) {
            return ResponseUtil.badArgument();
        }

        // 如果订单不是已付款状态，则不能发货
        if (!order.getOrderStatus().equals(OrderUtil.STATUS_PAY)) {
            logger.info("商场管理->订单管理->订单发货失败:{}", ORDER_CONFIRM_NOT_ALLOWED.desc());
            return AdminResponseUtil.fail(ORDER_CONFIRM_NOT_ALLOWED);
        }

        // 如果订单快递，则需要调用第三方配送平台，配送中
        // 如果是自提，则状态直接变成已收货
        // 配送方式 ：0 快递, 1 自提
        String deleveryOrderId = null;
        if (Objects.equals(order.getFreightType(), Byte.valueOf("0"))) {
            logger.info("商场管理->订单管理->订单:{}，配送方式为快递，推送第三方配送单", order.getOrderSn());
            order.setOrderStatus(OrderUtil.STATUS_SHIP);
        } else {
            order.setOrderStatus(OrderUtil.STATUS_CONFIRM);
        }
        // 推送下聚单客
        // 地图距离  对接第三方配送单接口
        List<TpmOrderGoods> orderGoods = orderGoodsService.queryByOid(orderId);
        deleveryOrderId = jdkOrderService.preCreateOrder(order, orderGoods);
        order.setDeliveryOrderId(deleveryOrderId);

        order.setShipSn(shipSn);
        order.setShipChannel(shipChannel);
        order.setShipTime(LocalDateTime.now());
        if (orderService.updateWithOptimisticLocker(order) == 0) {
            logger.info("商场管理->订单管理->订单发货失败:{}", "更新数据失败!");
            return ResponseUtil.updatedDateExpired();
        }

        // TODO 发送邮件和短信通知，这里采用异步发送
        // 发货会发送通知短信给用户: *
        // "您的订单已经发货，快递公司 {1}，快递单 {2} ，请注意查收"
//		notifyService.notifySmsTemplate(order.getMobile(), NotifyType.SHIP, new String[] { shipChannel, shipSn });

        logger.info("【请求结束】商场管理->订单管理->订单发货,响应结果:{}", "成功!");
        return ResponseUtil.ok();
    }

    /**
     * 回复订单商品
     *
     * @param body 订单信息，{ orderId：xxx }
     * @return 订单操作结果 成功则 { errno: 0, errmsg: '成功' } 失败则 { errno: XXX, errmsg: XXX }
     */
    public Object reply(String body) {
        Integer commentId = JacksonUtil.parseInteger(body, "commentId");
        if (commentId == null || commentId == 0) {
            return ResponseUtil.badArgument();
        }
        // 目前只支持回复一次
        if (commentService.findById(commentId) != null) {
            logger.info("商场管理->订单管理->订单商品回复:{}", ORDER_REPLY_EXIST.desc());
            return AdminResponseUtil.fail(ORDER_REPLY_EXIST);
        }
        String content = JacksonUtil.parseString(body, "content");
        if (StringUtils.isEmpty(content)) {
            return ResponseUtil.badArgument();
        }
        // 创建评价回复
        TpmComment comment = new TpmComment();
        comment.setType((byte) 2);
        comment.setValueId(commentId);
        comment.setContent(content);
        comment.setUserId(0); // 评价回复没有用
        comment.setStar((short) 0); // 评价回复没有用
        comment.setHasPicture(false); // 评价回复没有用
        comment.setPicUrls(new String[]{}); // 评价回复没有用
        commentService.save(comment);

        logger.info("【请求结束】商场管理->订单管理->订单商品回复,响应结果:{}", "成功!");
        return ResponseUtil.ok();
    }

    /**
     * 快递公司列表
     *
     * @return
     */
    public Object listShipChannel() {
        List<Map<String, String>> vendorMaps = expressService.getAllVendor();
        List<Map<String, Object>> shipChannelList = new ArrayList<Map<String, Object>>(vendorMaps == null ? 0 : vendorMaps.size());
        for (Map<String, String> map : vendorMaps) {
            Map<String, Object> b = new HashMap<>(2);
            b.put("value", map.get("code"));
            b.put("label", map.get("name"));
            shipChannelList.add(b);
        }

        Map<String, Object> data = new HashMap<>();
        data.put("shipChannelList", shipChannelList);
        logger.info("获取已配置的快递公司总数：{}", shipChannelList.size());
        return ResponseUtil.ok(data);
    }

    public Object updateStatus(OrderUpdateStatusReqVo req) {
        TpmOrder order = orderService.findById(req.getOrderId());
        if (OrderUtil.isConfirmStatus(order) || OrderUtil.isAutoConfirmStatus(order)) {
            return ResponseUtil.fail("订单已完结无法修改状态");
        }
        orderService.updateStatus(req.getOrderId(), Short.valueOf(req.getOrderStatus()));
        return ResponseUtil.ok();
    }

    public Object delivery(Integer orderId) {
        try {
            orderService.delivery(orderId);
            return ResponseUtil.ok();
        } catch (RuntimeException e) {
            return ResponseUtil.fail(-1, e.getMessage());
        }
    }

    public Object finish(Integer orderId) {
        try {
            orderService.finish(orderId);
            return ResponseUtil.ok();
        } catch (RuntimeException e) {
            return ResponseUtil.fail(-1, e.getMessage());
        }
    }

    public Object prepare(Integer orderId) {
        try {
            TpmOrder order = orderService.findById(orderId);
            orderService.prepare(orderId);
            WechatNotifyUtils.wechatNoteSend(SystemConfig.getWechatNotifyUrl(), null, "订单号：" + order.getOrderSn() + "完成出餐");
            return ResponseUtil.ok();
        } catch (RuntimeException e) {
            return ResponseUtil.fail(-1, e.getMessage());
        }
    }

    public Object print(Integer orderId) {
        TpmOrder order = orderService.findById(orderId);
        List<TpmOrderGoods> orderGoods = orderGoodsService.queryByOid(orderId);
        TpmCouponCodeUseLog tpmCouponCodeUseLog = tpmCouponCodeUseLogService.findByOrderId(order.getId());
        try {
//            jdkOrderService.printerPrint(order, orderGoods, "后厨联", "195121");
            jdkOrderService.printerPrint(order, orderGoods, "商家联", "195118", tpmCouponCodeUseLog);
            jdkOrderService.printerPrint(order, orderGoods, "顾客联", "195117", tpmCouponCodeUseLog);
            return ResponseUtil.ok("打印成功");
        } catch (RuntimeException e) {
            return ResponseUtil.fail(-1, e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void refundPostProcess(TpmOrder order) {
        // 设置订单取消状态
        order.setOrderStatus(OrderUtil.STATUS_REFUND_CONFIRM);
        if (orderService.updateWithOptimisticLocker(order) == 0) {
            logger.info("商场管理->订单管理->订单退款失败:{}", "更新数据已失效");
            throw new RuntimeException("更新数据已失效");
        }

        // 商品货品数量增加
//        List<TpmOrderGoods> orderGoodsList = orderGoodsService.queryByOid(orderId);
//        for (TpmOrderGoods orderGoods : orderGoodsList) {
//            Integer productId = orderGoods.getProductId();
//            Short number = orderGoods.getNumber();
//            if (productService.addStock(productId, number) == 0) {
//                logger.info("商场管理->订单管理->订单退款失败:{}", "商品货品库存增加失败");
//                throw new RuntimeException("商品货品库存增加失败");
//            }
//        }
        List<TpmOrderGoods> orderGoodsList = orderGoodsService.queryByOid(order.getId());
        for (TpmOrderGoods orderGoods : orderGoodsList) {
            Integer goodsId = orderGoods.getGoodsId();
            Short number = orderGoods.getNumber();
            if (tpmGoodsService.addInventory(goodsId, number) == 0) {
                throw new RuntimeException("商品货品库存增加失败");
            }
        }

        // TODO 发送邮件和短信通知，这里采用异步发送
        // 退款成功通知用户, 例如“您申请的订单退款 [ 单号:{1} ] 已成功，请耐心等待到账。”
        // 注意订单号只发后6位
//        notifyService.notifySmsTemplate(order.getMobile(), NotifyType.REFUND,
//                new String[]{order.getOrderSn().substring(8, 14)});

        if (Objects.nonNull(order.getConfirmTime())) {
            // 积分逆向
            TpmPointDto tpmPointDto = new TpmPointDto();
            tpmPointDto.setUserId(order.getUserId());
            tpmPointDto.setOrderSn(order.getOrderSn());
            tpmPointDto.setAmount(order.getActualPrice().multiply(BigDecimal.valueOf(-1)));
            tpmPointDto.setDescription(order.getOrderSn() + " 订单退款,扣除积分=" + order.getActualPrice());
            pointService.addPoint(tpmPointDto);
            logger.info("积分扣除成功,用户id={},订单号={},扣除积分={}", order.getUserId(), order.getOrderSn(), order.getActualPrice());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public String wxRefundNotify(String body, SignatureHeader signatureHeader) {
        logger.info("wxRefundNotify body={}", body);
        WxPayPartnerRefundNotifyV3Result wxPayPartnerRefundNotifyV3Result = null;
        try {
            //验签并解析结果
            wxPayPartnerRefundNotifyV3Result = wxPayService.parsePartnerRefundNotifyV3Result(body, signatureHeader);
            logger.info("wxRefundNotify result={}", JSON.toJSONString(wxPayPartnerRefundNotifyV3Result));
        } catch (WxPayException e) {
            logger.error("wxRefundNotify 解析失败", e);
            return WxPayNotifyV3Response.fail(e.getMessage());
        }
        WxPayPartnerRefundNotifyV3Result.DecryptNotifyResult result = wxPayPartnerRefundNotifyV3Result.getResult();
        String orderSn = result.getOutTradeNo();
        WxRefundOrderStatusEnums refundOrderStatusEnums = WxRefundOrderStatusEnums.getByCode(result.getRefundStatus());
        if (refundOrderStatusEnums == null) {
            logger.error("wxRefundNotify 状态解析失败");
            return WxPayNotifyV3Response.fail("退款状态解析失败");
        }
        try {
            //通知企微
            notifyWx(wxPayPartnerRefundNotifyV3Result, result, orderSn, refundOrderStatusEnums);
        } catch (Exception e) {
            logger.error("通知企业微信失败 error={}", e.getMessage(), e);
        }
        if (Objects.nonNull(orderSn) && orderSn.contains("-")) {
            String outRefundNo = result.getOutRefundNo();
            if (outRefundNo.contains("refund_")) {
                String[] split = outRefundNo.split("_");
                orderSn = split[1];
            }
        }
        TpmOrder tpmOrder = orderService.findBySn(orderSn);
        if (tpmOrder == null) {
            logger.error("wxRefundNotify orderSn={} 订单不存在", orderSn);
            return WxPayNotifyV3Response.fail("订单不存在");
        }
        if (OrderUtil.isRefundConfirmStatus(tpmOrder)) {
            logger.info("wxRefundNotify orderSn={}, 已退款完成 不处理", orderSn);
            return WxPayNotifyV3Response.success("");
        }
        String refundStatus = result.getRefundStatus();
        tpmOrder.setRefundStatus(refundStatus);
        if (Objects.equals(refundStatus, WxRefundOrderStatusEnums.SUCCESS.getCode())) {
            // 只有退款成功才进行后置处理
            refundPostProcess(tpmOrder);
            if (Objects.equals(tpmOrder.getBusinessType(), TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())){
                tpmCouponUserService.giveBackCoupon(orderSn);
            }
        } else {
            if (orderService.updateWithOptimisticLocker(tpmOrder) == 0) {
                logger.info("wxRefundNotify orderSn={} 更新数据已失效", orderSn);
                return WxPayNotifyV3Response.fail("数据已失效");
            }
        }
        logger.info("wxRefundNotify orderSn={} 退款成功", orderSn);
        return WxPayNotifyV3Response.success("");
    }


    public Object verifyQRCode(String qrCode) {
        logger.info("verifyQRCode qrCode={}", qrCode);
        String prefix = "coupon-";
        Boolean isCoupon = qrCode.startsWith(prefix);
        if (isCoupon) {
            try {
                String real_qrCode = qrCode.substring(prefix.length());
                TpmCouponUser tpmCouponUser = tpmCouponUserService.verifyCouponQrCode(real_qrCode);
                if (Objects.isNull(tpmCouponUser)) {
                    return ResponseUtil.fail(-1, "优惠券核销失败：关联优惠券未找到");
                }
                Integer couponId = tpmCouponUser.getCouponId();
                Integer userId = tpmCouponUser.getUserId();
                TpmCoupon tpmCoupon = tpmCouponService.findById(couponId);
                if (Objects.isNull(tpmCoupon)) {
                    return ResponseUtil.fail(-1, "优惠券核销失败：优惠券未找到");
                }
                TpmUser user = userService.findById(userId);
                if (Objects.isNull(user)) {
                    return ResponseUtil.fail(-1, "优惠券核销失败：用户未找到");
                }
                WechatNotifyUtils.wechatNoteSend(SystemConfig.getWechatNotifyUrl(), null, "用户：" + user.getNickname() + "会员卡号:" + user.getMembershipCode() + "优惠券核销成功：" + tpmCoupon.getName());

                return ResponseUtil.ok("优惠券核销成功");
            } catch (Exception e) {
                logger.error("verifyQRCode error={}", e.getMessage(), e);
                return ResponseUtil.fail(-1, "优惠券核销失败：" + e.getMessage());
            }
        } else {
            TpmOrder tpmOrder = null;
            try {
                tpmOrder = orderService.verifyQRCode(qrCode);
                WechatNotifyUtils.wechatNotifyMd(SystemConfig.getWechatNotifyUrl(), null, "订单号:" + tpmOrder.getOrderSn() + "已核销");
                if (Objects.equals(tpmOrder.getPayType(), OrderPayTypeEnums.WECHAT.getCode())) {
                    log.info("订单:{} 核销完成 上传发货信息", tpmOrder.getOrderSn());
                    TpmOrder finalTpmOrder = tpmOrder;
                    executor.submit(() -> wxUploadShippingInfoUtils.doUploadShippingInfo(finalTpmOrder.getId()));
                }
            } catch (RuntimeException e) {
                logger.error("verifyQRCode error={}", e.getMessage(), e);
                return ResponseUtil.fail(-1, "取餐失败：" + e.getMessage());
            }
//        try {
//            if (Objects.nonNull(tpmOrder)) {
//                if (!Objects.equals(tpmOrder.getFreightType(), (byte) 0))
//                    //自提or堂食 回传聚单客订单完成
//                    jdkOrderService.orderCompleted(tpmOrder);
//            }
//        } catch (Exception e) {
//            logger.error("聚单客订单完成失败", e);
//        }
            return ResponseUtil.ok(tpmOrder.getMealCode() + "取餐成功");
        }
    }
}
