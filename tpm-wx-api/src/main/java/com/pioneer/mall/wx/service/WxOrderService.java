package com.pioneer.mall.wx.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.binarywang.wxpay.bean.notify.CombineNotifyResult;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import com.github.binarywang.wxpay.bean.request.CombineTransactionsRequest;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.bean.result.BaseWxPayResult;
import com.github.binarywang.wxpay.bean.result.CombineTransactionsResult;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.pagehelper.PageInfo;
import com.pioneer.mall.core.config.CodeDiscountConfigDTO;
import com.pioneer.mall.core.config.EveningDiscountConfigDTO;
import com.pioneer.mall.core.consts.CommConsts;
import com.pioneer.mall.core.express.ExpressService;
import com.pioneer.mall.core.express.dao.ExpressInfo;
import com.pioneer.mall.core.express.dao.Traces;
import com.pioneer.mall.core.judanke.JdkOrderService;
import com.pioneer.mall.core.notify.NotifyService;
import com.pioneer.mall.core.qcode.QCodeService;
import com.pioneer.mall.core.spPrint.PrintService;
import com.pioneer.mall.core.system.DiscountService;
import com.pioneer.mall.core.system.SystemConfig;
import com.pioneer.mall.core.type.OrderFreightTypeEnum;
import com.pioneer.mall.core.util.*;
import com.pioneer.mall.core.vo.TpmOrderGoodsResVo;
import com.pioneer.mall.db.bean.dto.CartCheckOutCouponInfoDTO;
import com.pioneer.mall.db.bean.dto.TpmGoodsFullInfoDTO;
import com.pioneer.mall.db.bean.search.TpmOrderSearch;
import com.pioneer.mall.db.dao.TpmBalanceMapper;
import com.pioneer.mall.db.domain.*;
import com.pioneer.mall.db.dto.TpmGoodsAttributesDto;
import com.pioneer.mall.db.dto.TpmPointDto;
import com.pioneer.mall.db.dto.TpmShopOpenStatusDTO;
import com.pioneer.mall.db.dto.TpmSpecificationsResultDto;
import com.pioneer.mall.db.enums.OrderPayTypeEnums;
import com.pioneer.mall.db.enums.StatusEnum;
import com.pioneer.mall.db.enums.TpmBusinessTypeEnums;
import com.pioneer.mall.db.enums.TpmTransportTypeEnums;
import com.pioneer.mall.db.service.*;
import com.pioneer.mall.db.util.*;
import com.pioneer.mall.wx.bean.req.*;
import com.pioneer.mall.wx.bean.res.*;
import com.pioneer.mall.wx.dao.BrandOrderGoods;
import com.pioneer.mall.wx.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.pioneer.mall.db.util.WxResponseCode.*;

/**
 * 订单服务
 *
 * <p>
 * 订单状态： 101 订单生成，未支付；102，下单后未支付用户取消；103，下单后未支付超时系统自动取消 201
 * 支付完成，商家未发货；202，订单生产，已付款未发货，但是退款取消； 301 商家发货，用户未确认； 401 用户确认收货； 402
 * 用户没有确认收货超过一定时间，系统自动确认收货；
 *
 * <p>
 * 用户操作： 当101用户未付款时，此时用户可以进行的操作是取消订单，或者付款操作 当201支付完成而商家未发货时，此时用户可以取消订单并申请退款
 * 当301商家已发货时，此时用户可以有确认收货的操作 当401用户确认收货以后，此时用户可以进行的操作是删除订单，评价商品，或者再次购买
 * 当402系统自动确认收货以后，此时用户可以删除订单，评价商品，或者再次购买
 *
 * <p>
 * 注意：目前不支持订单退货和售后服务
 */
@Slf4j
@Service
public class WxOrderService {
    @Autowired
    private TpmUserService userService;
    @Autowired
    private TpmOrderService orderService;
    @Autowired
    private TpmOrderGoodsService orderGoodsService;
    @Autowired
    private TpmAddressService addressService;
    @Autowired
    private TpmCartService cartService;
    @Autowired
    private TpmRegionService regionService;
    @Autowired
    private WxPayService wxPayService;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private TpmUserFormIdService formIdService;
    @Autowired
    private TpmGrouponRulesService grouponRulesService;
    @Autowired
    private TpmGrouponService grouponService;
    @Autowired
    private QCodeService qCodeService;
    @Autowired
    private ExpressService expressService;
    @Autowired
    private TpmCommentService commentService;
    @Autowired
    private TpmCouponUserService couponUserService;
    @Autowired
    private CouponVerifyService couponVerifyService;
    @Autowired
    private TpmAccountService accountService;
    @Autowired
    private TpmPointService tpmPointService;
    @Autowired
    private TpmMembershipService tpmMembershipService;
    @Autowired
    private TpmBalanceMapper tpmBalanceMapper;
    @Autowired
    private TpmGoodsService tpmGoodsService;
    @Autowired
    private JdkOrderService jdkOrderService;
    @Autowired
    private TpmShopHoursService tpmShopHoursService;
    @Autowired
    private TpmAttributeSpecificationService attributeSpecificationService;
    @Autowired
    private DiscountService discountService;
    @Autowired
    private TpmCouponCodeService tpmCouponCodeService;
    @Autowired
    private TpmCouponCodeUseLogService tpmCouponCodeUseLogService;
    @Autowired
    private TpmWaybillRouteService tpmWaybillRouteService;
    @Autowired
    private PrintService printService;
    @Autowired
    private WxUploadShippingInfoUtils wxUploadShippingInfoUtils;

    private String detailedAddress(TpmAddress tpmAddress) {
        return tpmAddress.getAddressLocation() + tpmAddress.getAddressDetail();
    }

    /**
     * 订单列表
     *
     * @param userId   用户ID
     * @param showType 订单信息： 0，全部订单； 1，待付款； 2，待发货； 3，待收货； 4，待评价。
     * @param page     分页页数
     * @param size     分页大小
     * @return 订单列表
     */
    public WxResult<TpmOrderListResVo> list(TpmOrderSearch tpmOrderSearch) {
        Integer userId = tpmOrderSearch.getUserId();
        if (userId == null) {
            TpmOrderListResVo emptyResVo = new TpmOrderListResVo();
            //前端说不要报错，给空对象
            return WxResult.success(emptyResVo);
        }
//        List<Short> orderStatus = OrderUtil.orderStatus(showType);
        List<TpmOrder> orderList = orderService.list(tpmOrderSearch);
        long count = PageInfo.of(orderList).getTotal();
        int totalPages = (int) Math.ceil((double) count / tpmOrderSearch.getPageSize());

        List<TpmOrderResVo> orderVoList = new ArrayList<>(orderList.size());
        for (TpmOrder order : orderList) {
            TpmOrderResVo tpmOrderResVo = new TpmOrderResVo();
            tpmOrderResVo.setId(order.getId());
            tpmOrderResVo.setOrderSn(order.getOrderSn());
            tpmOrderResVo.setAddTime(order.getAddTime());
            tpmOrderResVo.setConsignee(order.getConsignee());
            tpmOrderResVo.setMobile(order.getMobile());
            tpmOrderResVo.setAddress(order.getAddress());
            tpmOrderResVo.setGoodsPrice(order.getGoodsPrice());
            tpmOrderResVo.setFreightPrice(order.getFreightPrice());
            tpmOrderResVo.setActualPrice(order.getActualPrice());
            tpmOrderResVo.setOrderStatusText(OrderUtil.orderStatusText(order));
            tpmOrderResVo.setHandleOption(OrderUtil.build(order));
            tpmOrderResVo.setExpCode(order.getShipChannel());
            tpmOrderResVo.setExpNo(order.getShipSn());
            tpmOrderResVo.setMealCode(order.getMealCode());
            tpmOrderResVo.setMealQrCode(order.getMealQrCode());
            tpmOrderResVo.setMealPickupStatus(order.getMealPickupStatus());
//            TpmGroupon groupon = grouponService.queryByOrderId(order.getId());
//            if (groupon != null) {
//                tpmOrderResVo.setGroupin(true);
//            } else {
//                tpmOrderResVo.setGroupin(false);
//            }
            List<TpmOrderGoods> orderGoodsList = orderGoodsService.queryByOid(order.getId());
            List<TpmOrderGoodsResVo> goodsResVoList = GoodsSpecificationUtil.getOrderGoodsResVoList(orderGoodsList);
            /*
             * List<Map<String, Object>> orderGoodsVoList = new
             * ArrayList<>(orderGoodsList.size()); for (TpmOrderGoods orderGoods :
             * orderGoodsList) { Map<String, Object> orderGoodsVo = new HashMap<>();
             * orderGoodsVo.put("id", orderGoods.getId()); orderGoodsVo.put("goodsName",
             * orderGoods.getGoodsName()); orderGoodsVo.put("number",
             * orderGoods.getNumber()); orderGoodsVo.put("picUrl", orderGoods.getPicUrl());
             * orderGoodsVo.put("price", orderGoods.getPrice()); orderGoodsVo.put("picUrl",
             * orderGoods.getPicUrl()); orderGoodsVoList.add(orderGoodsVo); }
             */
            tpmOrderResVo.setGoodsList(goodsResVoList);
            tpmOrderResVo.setShowMealQrCode(!this.isFinishTakeMeal(order));
            orderVoList.add(tpmOrderResVo);
        }
        TpmOrderListResVo tpmOrderListResVo = new TpmOrderListResVo();
        tpmOrderListResVo.setCount(count);
        tpmOrderListResVo.setData(orderVoList);
        tpmOrderListResVo.setTotalPages(totalPages);

        log.info("【请求结束】获取订单列表,响应结果:{}", JSONObject.toJSONString(tpmOrderListResVo));
        return WxResult.success(tpmOrderListResVo);
    }

    /**
     * 订单详情
     *
     * @param userId  用户ID
     * @param orderId 订单ID
     * @return 订单详情
     */
    public WxResult<TpmOrderResVo> detail(Integer userId, Integer orderId) {
        if (userId == null) {
            log.error("获取订单详情失败：用户未登录!");
            return WxResult.unlogin();
        }

        // 订单信息
        TpmOrder order = orderService.findById(orderId);
        if (null == order) {
            log.error("获取订单详情失败：{}", ORDER_UNKNOWN.desc());
            return WxResult.error(ORDER_UNKNOWN);
        }
        if (!order.getUserId().equals(userId)) {
            log.error("获取订单详情失败：{}", ORDER_INVALID.desc());
            return WxResult.error(ORDER_INVALID);
        }
        TpmOrderResVo tpmOrderResVo = new TpmOrderResVo();
        tpmOrderResVo.setId(order.getId());
        tpmOrderResVo.setOrderSn(order.getOrderSn());
        tpmOrderResVo.setAddTime(order.getAddTime());
        tpmOrderResVo.setConsignee(order.getConsignee());
        tpmOrderResVo.setMobile(order.getMobile());
        tpmOrderResVo.setAddress(order.getAddress());
        tpmOrderResVo.setGoodsPrice(order.getGoodsPrice());
        tpmOrderResVo.setFreightPrice(order.getFreightPrice());
        tpmOrderResVo.setPackingFee(order.getPackingFee());
        tpmOrderResVo.setDiscountPrice(order.getIntegralPrice().add(order.getGrouponPrice()).add(order.getCouponPrice()));
        tpmOrderResVo.setActualPrice(order.getActualPrice());
        tpmOrderResVo.setOrderStatus(order.getOrderStatus().intValue());
        tpmOrderResVo.setOrderStatusText(OrderUtil.orderStatusText(order));
        tpmOrderResVo.setHandleOption(OrderUtil.build(order));
        tpmOrderResVo.setExpCode(order.getShipChannel());
        tpmOrderResVo.setExpNo(order.getShipSn());
        tpmOrderResVo.setPoint(order.getActualPrice());
        tpmOrderResVo.setMealCode(order.getMealCode());
        tpmOrderResVo.setMealQrCode(order.getMealQrCode());
        tpmOrderResVo.setMealPickupStatus(order.getMealPickupStatus());
        tpmOrderResVo.setShopPhone(SystemConfig.getXinChenTelephone());
        List<TpmOrderGoods> orderGoodsList = orderGoodsService.queryByOid(order.getId());
        List<TpmOrderGoodsResVo> goodsResVoList = GoodsSpecificationUtil.getOrderGoodsResVoList(orderGoodsList);
        tpmOrderResVo.setGoodsList(goodsResVoList);
        tpmOrderResVo.setFreightType(order.getFreightType().intValue());
        // 订单状态为已发货且物流信息不为空
        // "YTO", "800669400640887922"
        /*
         * if (order.getOrderStatus().equals(OrderUtil.STATUS_SHIP)) { ExpressInfo ei =
         * expressService.getExpressInfo(order.getShipChannel(), order.getShipSn());
         * result.put("expressInfo", ei); }
         */
        if (Objects.equals(order.getPayType(), OrderPayTypeEnums.WECHAT.getCode())) {
            tpmOrderResVo.setPayType(OrderPayTypeEnums.WECHAT.getDesc());
        } else if (Objects.equals(order.getPayType(), OrderPayTypeEnums.BALANCE.getCode())) {
            tpmOrderResVo.setPayType(OrderPayTypeEnums.BALANCE.getDesc());
        } else {
            tpmOrderResVo.setPayType("其他支付");
        }
        log.info("【请求结束】获取订单详情,响应结果:{}", JSONObject.toJSONString(tpmOrderResVo));
        return WxResult.success(tpmOrderResVo);

    }

    public WxResult<List<TpmWaybillRouteResVo>> getWaybillRoute(Integer tpmOrderId, Integer userId) {
        log.info("【请求开始】获取运单详情,请求参数:{}", JSONObject.toJSONString(tpmOrderId));
        if (Objects.isNull(tpmOrderId)) {
            return WxResult.badArgument();
        }
        if (Objects.isNull(userId)) {
            log.error("提交订单详情失败：用户未登录!");
            return WxResult.unlogin();
        }
        TpmOrder order = orderService.findById(tpmOrderId);
        if (Objects.isNull(order)) {
            log.error("获取运单详情失败：订单不存在!");
            return WxResult.error("未查询到订单");
        }
        List<TpmWaybillRoute> tpmWaybillRoutes = tpmWaybillRouteService.findByTpmOrderId(tpmOrderId);
        List<TpmWaybillRouteResVo> tpmWaybillRouteResVos = ConvertUtil.listConvert(tpmWaybillRoutes, TpmWaybillRouteResVo.class);
        //将list倒序展示
        Collections.reverse(tpmWaybillRouteResVos);
        log.info("【请求结束】获取运单详情,响应结果:{}", JSONObject.toJSONString(tpmWaybillRouteResVos));
        return WxResult.success(tpmWaybillRouteResVos);
    }

    /**
     * 提交订单
     * <p>
     * 1. 创建订单表项和订单商品表项; 2. 购物车清空; 3. 优惠券设置已用; 4. 商品货品库存减少; 5. 如果是团购商品，则创建团购活动表项。
     *
     * @param userId 用户ID
     * @param body   订单信息，{ cartId：xxx, addressId: xxx, couponId: xxx, message: xxx,
     *               grouponRulesId: xxx, grouponLinkId: xxx}
     * @return 提交订单操作结果
     */
    @Transactional
    public WxResult<Integer> submit(TpmOrderSubmitReqVo tpmOrderSubmitReqVo) {
        log.info("用户下单 submit req={}", JSONObject.toJSONString(tpmOrderSubmitReqVo));
        if (Objects.isNull(tpmOrderSubmitReqVo)) {
            return WxResult.badArgument();
        }
        if (Objects.isNull(tpmOrderSubmitReqVo.getUserId())) {
            log.error("提交订单详情失败：用户未登录!");
            return WxResult.unlogin();
        }
        if (Objects.isNull(tpmOrderSubmitReqVo.getFreightType())) {
            log.error("提交订单详情失败：订单类型不能为空!");
            return WxResult.error(-1, "订单类型不能为空");
        }
        TpmShopOpenStatusDTO shopOpenStatus = tpmShopHoursService.getShopOpenStatus(tpmOrderSubmitReqVo.getShopId());
        if (Objects.isNull(shopOpenStatus)) {
            log.error("提交订单详情失败：店铺未配置营业时间!");
            return WxResult.error(-1, "店铺未配置营业时间");
        }
        if (!shopOpenStatus.getOpen()) {
            String statusDesc = shopOpenStatus.getStatusDesc();
            if (Objects.isNull(statusDesc)) {
                statusDesc = "店铺未配置营业时间";
            }
            log.error("提交订单详情失败：{}!", statusDesc);
            return WxResult.error(-1, statusDesc);
        }
        CodeDiscountConfigDTO codeDiscountConfigDTO = null;
        if (!StringUtils.isEmpty(tpmOrderSubmitReqVo.getCouponCode())) {
            try {
                codeDiscountConfigDTO = discountService.getCouponCodeDiscount(tpmOrderSubmitReqVo.getCouponCode());
                log.info("获取优惠券折扣成功：{}", JSONObject.toJSONString(codeDiscountConfigDTO));
            } catch (Exception e) {
                log.error("获取优惠券折扣失败：" + e.getMessage(), e);
                return WxResult.error(e.getMessage());
            }
        }
        Integer userId = tpmOrderSubmitReqVo.getUserId();
        TpmUser tpmUser = userService.findById(userId);
        if (Objects.isNull(tpmUser)) {
            return WxResult.userNotExist();
        }
        Map<Integer, String> cartIdRemarkMap = tpmOrderSubmitReqVo.getCartIdRemarkMap();
        log.info("cartIdRemarkMap={}", JSONObject.toJSONString(cartIdRemarkMap));
        List<Integer> cartIdList = cartIdRemarkMap.keySet().stream().collect(Collectors.toList());
        log.info("cartIdList={}", JSONObject.toJSONString(cartIdList));
        Integer addressId = tpmOrderSubmitReqVo.getAddressId();
        Integer couponUserId = tpmOrderSubmitReqVo.getCouponUserId();
        String remark = tpmOrderSubmitReqVo.getRemark();
        Byte freightType = tpmOrderSubmitReqVo.getFreightType();
        if (CollectionUtils.isEmpty(cartIdList) || couponUserId == null) {
            return WxResult.badArgument();
        }
        TpmAddress checkedAddress = null;
        //0为外卖
        if (Objects.equals(freightType, (byte) 0)) {
            if (addressId == null) {
                return WxResult.error("收货地址未选择");
            }
            // 收货地址
            checkedAddress = addressService.findById(addressId);
            if (checkedAddress == null) {
                return WxResult.error(-1, "收货地址不存在");
            }
            if (Objects.isNull(checkedAddress.getLatitude()) || Objects.isNull(checkedAddress.getLongitude())) {
                return WxResult.error(-1, "请在地址管理中重新设置地址");
            }
        } else if (Objects.equals(freightType, (byte) 1) || Objects.equals(freightType, (byte) 2)) {
            if (Objects.isNull(tpmOrderSubmitReqVo.getTelephone()) || StringUtils.isBlank(tpmOrderSubmitReqVo.getTelephone())) {
                return WxResult.error(-1, "请填写联系号码");
            }
        }
        // 货品价格
        List<TpmCart> checkedGoodsList = cartService.findById(cartIdList);

        if (checkedGoodsList.isEmpty()) {
            return WxResult.error("已选中商品为空");
        }

        List<Integer> goodsIdList = checkedGoodsList.stream().map(TpmCart::getGoodsId).distinct().collect(Collectors.toList());
//        List<TpmGoods> goodsList = tpmGoodsService.findById(goodsIdList);
        List<TpmGoodsFullInfoDTO> fullInfoDTOByIds = tpmGoodsService.findFullInfoDTOByIds(goodsIdList);

        List<TpmGoods> goodsList = fullInfoDTOByIds.stream().map(TpmGoodsFullInfoDTO::getTpmGoods).collect(Collectors.toList());
        Map<Integer, TpmGoods> goodsIdMap = goodsList.stream().collect(Collectors.toMap(TpmGoods::getId, Function.identity(), (v1, v2) -> v1));
        Map<Integer, List<TpmGoodsAttributesDto>> goodsIdAttributesMap = fullInfoDTOByIds.stream().collect(Collectors.toMap(fullInfo -> fullInfo.getTpmGoods().getId(), fullInfo -> fullInfo.getGoodsAttributes()));
//        TpmGoods notOnSale = goodsList.stream().filter(tpmGoods -> !tpmGoods.getIsOnSale()).findAny().orElse(null);
//        if (Objects.nonNull(notOnSale)) {
//            return WxResult.error("商品" + notOnSale.getName() + "已下架");
//        }
//        TpmGoods sellOut = goodsList.stream().filter(TpmGoods::getIsSellOut).findAny().orElse(null);
//        if (Objects.nonNull(sellOut)) {
//            return WxResult.error("商品" + sellOut.getName() + "已售罄");
//        }
        for (TpmCart cart : checkedGoodsList) {
            if (!goodsIdMap.containsKey(cart.getGoodsId())) {
                return WxResult.error("商品:" + cart.getGoodsName() + "已下架");
            }
            TpmGoods tpmGoods = goodsIdMap.get(cart.getGoodsId());
            if (Objects.isNull(tpmGoods)) {
                return WxResult.error("商品:" + cart.getGoodsName() + "已下架");
            }
            if (!tpmGoods.getIsOnSale()) {
                return WxResult.error("商品:" + tpmGoods.getName() + "已下架");
            }
            if (tpmGoods.getIsSellOut()) {
                return WxResult.error("商品:" + tpmGoods.getName() + "已售罄");
            }
            if (Objects.isNull(tpmGoods.getInventoryNum()) || tpmGoods.getInventoryNum() <= 0) {
                return WxResult.error("商品:" + tpmGoods.getName() + "已售罄");
            }
            List<TpmGoodsAttributesDto> tpmGoodsAttributesDtos = goodsIdAttributesMap.get(tpmGoods.getId());
            String specifications = cart.getSpecifications();
            if (Objects.isNull(specifications) && !CollectionUtils.isEmpty(tpmGoodsAttributesDtos)) {
                return WxResult.error("商品:" + tpmGoods.getName() + "商品属性发生变化,请重新添加");
            }
            if (Objects.nonNull(specifications) && CollectionUtils.isEmpty(tpmGoodsAttributesDtos)) {
                return WxResult.error("商品:" + tpmGoods.getName() + "商品属性发生变化,请重新添加");
            }
            Boolean inDbSaleRange = SaleRangeEnum.pageSaleRangeInDbSaleRange(Integer.valueOf(tpmOrderSubmitReqVo.getFreightType()), tpmGoods.getSaleRange());
            if (!inDbSaleRange) {
                SaleRangeEnum saleRangeEnum = SaleRangeEnum.getEnumByPageSaleRange(Integer.valueOf(tpmOrderSubmitReqVo.getFreightType()));
                if (saleRangeEnum != null) {
                    return WxResult.error("商品:" + tpmGoods.getName() + "不允许" + saleRangeEnum.getDesc());
                }
            }
            List<TpmGoodsAttributesDto> cartAttributes = new ArrayList<>();
            if (StringUtils.isNoneBlank(cart.getSpecifications())) {
                cartAttributes = JSONArray.parseArray(cart.getSpecifications(), TpmGoodsAttributesDto.class);
            }
            // 比较属性
            String validateResult = attributeSpecificationService.attributesSpecificationValid(cartAttributes, tpmGoodsAttributesDtos);
            if (StringUtils.isNoneBlank(validateResult)) {
                return WxResult.error(validateResult);
            }

        }

        EveningDiscountConfigDTO currentEveningDiscount = discountService.getCurrentEveningDiscount();
        Boolean effectiveEveningDiscount = currentEveningDiscount.getEffective();
        BigDecimal discount = currentEveningDiscount.getDiscount();
        List<Integer> excludeCategory = currentEveningDiscount.getExcludeCategory();

        BigDecimal goodsTotalPrice = new BigDecimal("0.00");// 商品总价 （包含团购减免，即减免团购后的商品总价，多店铺需将所有商品相加）
        BigDecimal totalFreightPrice = new BigDecimal("0.00");// 总配送费 （单店铺模式一个，多店铺模式多个配送费的总和）
        BigDecimal packingFee = new BigDecimal("0.00");// 总配送费 （单店铺模式一个，多店铺模式多个配送费的总和）
        if (Objects.nonNull(tpmOrderSubmitReqVo.getFreightPrice())) {
            totalFreightPrice = tpmOrderSubmitReqVo.getFreightPrice();
        }
        if (Objects.nonNull(tpmOrderSubmitReqVo.getPackingFee())) {
            packingFee = tpmOrderSubmitReqVo.getPackingFee();
        }

        BigDecimal discountPrice = new BigDecimal(0.00);

        for (TpmCart checkGoods : checkedGoodsList) {
            Integer goodsId = checkGoods.getGoodsId();
            if (goodsIdMap.containsKey(goodsId)) {
                // 解析下属性
                BigDecimal specificationsPrice = new BigDecimal(0.00);
                String specifications = checkGoods.getSpecifications();
                if (StringUtils.isNotEmpty(specifications)) {
                    List<TpmGoodsAttributesDto> goodsAttributesDtos = JSONArray.parseArray(specifications, TpmGoodsAttributesDto.class);
                    // 验证下属性必选是否填写
//                    String validateResult = attributeSpecificationService.attributesSpecificationValid(goodsAttributesDtos,goodsId);
//                    if (StringUtils.isNoneBlank(validateResult)) {
//                        return WxResult.error(validateResult);
//                    }
                    TpmSpecificationsResultDto specificationsResultDto = attributeSpecificationService.getSelectedSpecificationsResult(goodsAttributesDtos);
                    if (specificationsResultDto != null) {
                        specificationsPrice = specificationsResultDto.getSpecificationTotalPrice();
                    }
                }
                TpmGoods tpmGoods = goodsIdMap.get(goodsId);
                checkGoods.setGoodsName(tpmGoods.getName());
                checkGoods.setPicUrl(tpmGoods.getPicUrl());
//                checkGoods.setPrice(tpmGoods.getRetailPrice());
                BigDecimal retailPrice = tpmGoods.getRetailPrice().add(specificationsPrice);
                //原价
                BigDecimal originalPrice = tpmGoods.getRetailPrice().add(specificationsPrice);
                if (effectiveEveningDiscount && !excludeCategory.contains(tpmGoods.getCategoryId())) {
                    retailPrice = retailPrice.multiply(discount);
                }
                //优惠口令折扣
                if (Objects.nonNull(codeDiscountConfigDTO) && codeDiscountConfigDTO.getEffective()) {
                    BigDecimal codeDiscount = codeDiscountConfigDTO.getDiscount();
                    retailPrice = retailPrice.multiply(codeDiscount);
                }
                checkGoods.setPrice(retailPrice);
                goodsTotalPrice = goodsTotalPrice.add(retailPrice.multiply(new BigDecimal(checkGoods.getNumber())));
                discountPrice = discountPrice.add(originalPrice.subtract(retailPrice).multiply(new BigDecimal(checkGoods.getNumber())));
            } else {
                return WxResult.error("商品" + checkGoods.getGoodsName() + "已下架");
            }
        }
        // 根据订单商品总价计算运费，满足条件（例如66元）则免运费，否则需要支付运费（例如6元）；
//        if (goodsTotalPrice.compareTo(SystemConfig.getFreightLimit()) < 0) {
//            totalFreightPrice = SystemConfig.getFreight();
//        }
        // 获取可用的优惠券信息 使用优惠券减免的金额
        BigDecimal couponPrice = new BigDecimal(0.00);

        // 如果couponId=0则没有优惠券，couponId=-1则不使用优惠券
        CartCheckOutCouponInfoDTO cartCheckOutCouponInfoDTO = null;
        if (couponUserId != 0 && couponUserId != -1) {
            Map<Integer, TpmGoods> idTpmGoodsMap = goodsList.stream().collect(Collectors.toMap(TpmGoods::getId, Function.identity(), (v1, v2) -> v1));
            try {
                cartCheckOutCouponInfoDTO = couponVerifyService.checkCouponWithGoods(userId, idTpmGoodsMap, goodsTotalPrice, couponUserId);
                couponPrice = cartCheckOutCouponInfoDTO.getCouponPrice();
                //cartCheckOutCouponInfoDTO.getCategoryId() 会有空指针异常的风险 注意
                if (Objects.equals(cartCheckOutCouponInfoDTO.getGoodsType(), 2)) {
                    if (effectiveEveningDiscount && !excludeCategory.contains(cartCheckOutCouponInfoDTO.getCategoryId())) {
                        couponPrice = couponPrice.multiply(discount);
                    }
                    if (Objects.nonNull(codeDiscountConfigDTO) && codeDiscountConfigDTO.getEffective()) {
                        couponPrice = couponPrice.multiply(codeDiscountConfigDTO.getDiscount());
                    }
                }
            } catch (Exception e) {
                return WxResult.error("优惠券计算失败哦");
            }
//            TpmCoupon coupon = couponVerifyService.checkCoupon(userId, couponId, goodsTotalPrice);
//            if (coupon == null) {
//                return WxResult.badArgumentValue();
//            }
//            couponPrice = coupon.getDiscount();
        }

        // 可以使用的其他钱，例如用户积分
        BigDecimal integralPrice = new BigDecimal(0.00);

        // 订单费用
        BigDecimal orderTotalPrice = goodsTotalPrice.add(totalFreightPrice).add(packingFee).subtract(couponPrice);
        // 最终支付费用
        BigDecimal actualPrice = orderTotalPrice.subtract(integralPrice);
//        if (Objects.nonNull(tpmUser.getMembershipId())) {
//            TpmMembership tpmMembership = tpmMembershipService.findById(tpmUser.getMembershipId());
//            if (Objects.nonNull(tpmMembership)) {
//                Integer discount = tpmMembership.getDiscount();
//                if (Objects.nonNull(discount)) {
//                    //以会员折后的价格
//                    actualPrice = actualPrice.multiply(new BigDecimal(discount)).divide(new BigDecimal(100));
//                }
//            }
//        }

        Integer orderId = null;
        TpmOrder order = null;
        // 订单
        order = new TpmOrder();
        // 设置下收货地址经纬度
        if (Objects.equals(tpmOrderSubmitReqVo.getFreightType(), (byte) 0)) {
            String join = checkedAddress.getLongitude() + "," + checkedAddress.getLatitude();
            order.setLngAndLat(join);
        }

        order.setUserId(userId);
        order.setOrderSn(orderService.generateOrderSn(userId));
        order.setOrderStatus(OrderUtil.STATUS_CREATE);
        order.setMessage(remark);
        if (Objects.equals(tpmOrderSubmitReqVo.getFreightType(), (byte) 0)) {
            order.setConsignee(checkedAddress.getName());
            order.setMobile(checkedAddress.getMobile());
            String detailedAddress = detailedAddress(checkedAddress);
            order.setAddress(detailedAddress);
        } else {
            order.setMobile(tpmOrderSubmitReqVo.getTelephone());
        }
        order.setGoodsPrice(goodsTotalPrice);
        order.setFreightPrice(totalFreightPrice);
        order.setPackingFee(packingFee);
        BigDecimal totalCouponPrice = couponPrice.add(discountPrice);
        order.setCouponPrice(totalCouponPrice);
        order.setIntegralPrice(integralPrice);
        order.setOrderPrice(orderTotalPrice);
        order.setActualPrice(actualPrice);
        order.setGrouponPrice(new BigDecimal(0.00)); // 团购价格
        order.setFreightType(freightType);
        String pickupCode = generatePickupCode(freightType);
        order.setMealCode(pickupCode);
        order.setMealPickupStatus(false);
        order.setMealQrCode(UUID.randomUUID().toString().replaceAll("-", ""));
        order.setBusinessType(TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode());
        // 添加订单表项
        orderService.add(order);
        orderId = order.getId();

        // 添加订单商品表项
        for (TpmCart cartGoods : checkedGoodsList) {
            String goodsRemark = cartIdRemarkMap.get(cartGoods.getId());
            // 订单商品
            TpmOrderGoods orderGoods = new TpmOrderGoods();
            orderGoods.setOrderId(order.getId());
            orderGoods.setGoodsId(cartGoods.getGoodsId());
            orderGoods.setGoodsSn(cartGoods.getGoodsSn());
//            orderGoods.setProductId(cartGoods.getProductId());
            orderGoods.setGoodsName(cartGoods.getGoodsName());
            orderGoods.setPicUrl(cartGoods.getPicUrl());
//            TpmGoods tpmGoods = goodsIdMap.get(cartGoods.getGoodsId());
            orderGoods.setPrice(cartGoods.getPrice());
//            if (effectiveEveningDiscount&&!excludeCategory.contains(tpmGoods.getCategoryId())){
//                orderGoods.setPrice(orderGoods.getPrice().multiply(discount));
//            }
//            if (Objects.nonNull(codeDiscountConfigDTO)&&codeDiscountConfigDTO.getEffective()){
//                BigDecimal codeDiscount = codeDiscountConfigDTO.getDiscount();
//                orderGoods.setPrice(orderGoods.getPrice().multiply(codeDiscount));
//            }
            orderGoods.setNumber(cartGoods.getNumber());
            orderGoods.setRemark(goodsRemark);
            orderGoods.setSpecifications(cartGoods.getSpecifications());
            orderGoods.setAddTime(LocalDateTime.now());

            orderGoods.setBrandId(cartGoods.getBrandId());// 订单商品需加上入驻店铺标志

            orderGoodsService.add(orderGoods);
            //扣减一下商品库存
            if (tpmGoodsService.reduceInventory(cartGoods.getGoodsId(), cartGoods.getNumber()) == 0) {
                return WxResult.error("商品库存扣减失败");
            }
        }
        // 删除购物车里面的商品信息
        cartService.clearGoods(userId);

        // 商品货品数量减少
//        for (TpmCart checkGoods : checkedGoodsList) {
//            Integer productId = checkGoods.getProductId();
//            TpmGoodsProduct product = productService.findById(productId);
//
//            Integer remainNumber = product.getNumber() - checkGoods.getNumber();
//            if (remainNumber < 0) {
//                throw new RuntimeException("下单的商品货品数量大于库存量");
//            }
//            if (productService.reduceStock(productId, checkGoods.getGoodsId(), checkGoods.getNumber()) == 0) {
//                throw new RuntimeException("商品货品库存减少失败");
//            }
//        }

        // 如果使用了优惠券，设置优惠券使用状态
        if (couponUserId != 0 && couponUserId != -1) {
            TpmCouponUser couponUser = couponUserService.findById(couponUserId);
            couponUser.setStatus(CouponUserConstant.STATUS_USED);
            couponUser.setUsedTime(LocalDateTime.now());
            couponUser.setOrderSn(order.getOrderSn());
            couponUserService.update(couponUser);
            if (Objects.nonNull(cartCheckOutCouponInfoDTO)) {
                WechatNotifyUtils.wechatNoteSend(SystemConfig.getWechatNotifyUrl(), null, "订单号：" + order.getOrderSn() + "使用优惠券:" + cartCheckOutCouponInfoDTO.getCouponName() + "，优惠:" + cartCheckOutCouponInfoDTO.getCouponPrice() + "元");
            }
        }
        if (Objects.nonNull(codeDiscountConfigDTO) && codeDiscountConfigDTO.getEffective()) {
            tpmCouponCodeService.reduceRemainingTimesAndLog(order, codeDiscountConfigDTO.getCouponCodeId());
        }

        log.info("【请求结束】提交订单,响应结果:{}", JSONObject.toJSONString(orderId));
        return WxResult.success(orderId);
    }

    @Transactional
    public WxResult<TpmShopSubmitResVo> shopSubmit(TpmOrderShopSubmitReqVo reqVo) {
        log.info("用户下单 submit req={}", JSONObject.toJSONString(reqVo));

        // 验证请求参数
        WxResult<TpmShopSubmitResVo> validationResult = validateRequest(reqVo);
        if (validationResult != null) {
            return validationResult;
        }

        // 获取用户信息
        Integer userId = reqVo.getUserId();
        TpmUser tpmUser = userService.findById(userId);
        if (Objects.isNull(tpmUser)) {
            return WxResult.userNotExist();
        }

        // 收货地址
        TpmAddress checkedAddress = validateAddress(reqVo.getAddressId());
        if (checkedAddress == null) {
            return WxResult.error(-1, "收货地址不存在");
        }
        if (Objects.isNull(checkedAddress.getLatitude()) || Objects.isNull(checkedAddress.getLongitude())) {
            return WxResult.error(-1, "请在地址管理中重新设置地址");
        }

        // 处理订单提交
        TpmShopSubmitResVo resVo = new TpmShopSubmitResVo();
        try {
            processSubmit(reqVo.getColdChainSubmit(), reqVo.getUserId(), checkedAddress, TpmTransportTypeEnums.COLD_CHAIN, resVo);
            processSubmit(reqVo.getCommonSubmit(), reqVo.getUserId(), checkedAddress, TpmTransportTypeEnums.COMMON, resVo);
            return WxResult.success(resVo);
        } catch (Exception e) {
            log.error("提交订单失败", e);
            return WxResult.error(e.getMessage());
        }
    }

    /**
     * 验证请求参数
     */
    private WxResult<TpmShopSubmitResVo> validateRequest(TpmOrderShopSubmitReqVo reqVo) {
        if (Objects.isNull(reqVo)) {
            return WxResult.badArgument();
        }
        if (Objects.isNull(reqVo.getUserId())) {
            log.error("提交订单详情失败：用户未登录!");
            return WxResult.unlogin();
        }
        return null;
    }

    /**
     * 验证地址
     */
    private TpmAddress validateAddress(Integer addressId) {
        if (addressId == null) {
            log.error("收货地址未选择");
            return null;
        }
        TpmAddress address = addressService.findById(addressId);
        if (address == null) {
            log.error("收货地址不存在, addressId={}", addressId);
            return null;
        }
        if (Objects.isNull(address.getLatitude()) || Objects.isNull(address.getLongitude())) {
            log.error("地址经纬度缺失, addressId={}", addressId);
            return null;
        }
        return address;
    }

    /**
     * 处理订单提交（冷链/普通）
     */
    public void processSubmit(TpmOrderSubmitReqVo submit, Integer userId, TpmAddress address,
                              TpmTransportTypeEnums transportType, TpmShopSubmitResVo resVo) throws Exception {
        if (Objects.nonNull(submit)) {
            submit.setUserId(userId);
            Integer orderId = handleSubmit(submit, address, transportType);
            if (transportType == TpmTransportTypeEnums.COLD_CHAIN) {
                resVo.setColdChainOrderId(orderId);
            } else {
                resVo.setCommonOrderId(orderId);
            }
        }
    }

    private Integer handleSubmit(TpmOrderSubmitReqVo submit, TpmAddress address, TpmTransportTypeEnums transportType) throws Exception {
        log.info("submit={} transportType={}", JSONObject.toJSONString(submit), transportType.getDesc());

        Map<Integer, String> cartIdRemarkMap = submit.getCartIdRemarkMap();
        List<Integer> cartIdList = new ArrayList<>(cartIdRemarkMap.keySet());
        if (CollectionUtils.isEmpty(cartIdList)) {
            log.info("购物车id没传");
            throw new Exception("购物车没有商品");
        }

        List<TpmCart> cartGoodsList = cartService.findById(cartIdList);
        if (cartGoodsList.isEmpty()) {
            log.info("数据库中购物车没有商品");
            throw new Exception("购物车没有商品");
        }

        // 获取商品信息
        List<Integer> goodsIdList = cartGoodsList.stream().map(TpmCart::getGoodsId).distinct().collect(Collectors.toList());
        List<TpmGoodsFullInfoDTO> fullInfoDTOByIds = tpmGoodsService.findFullInfoDTOByIds(goodsIdList);
        Map<Integer, TpmGoodsFullInfoDTO> goodsIdMap = fullInfoDTOByIds.stream().collect(Collectors.toMap(c -> c.getTpmGoods().getId(), Function.identity()));

        // 验证商品并计算总价
        BigDecimal goodsTotalPrice = validateShopGoodsAndCalculatePrice(cartGoodsList, goodsIdMap, transportType);

        // 创建订单
        TpmOrder order = createShopOrder(submit, address, goodsTotalPrice, transportType);
        orderService.add(order);
        int orderId = order.getId();
        log.info("创建订单成功, orderId={}", orderId);
        // 添加订单商品并扣减库存
        addOrderGoodsAndReduceInventory(orderId, cartGoodsList, cartIdRemarkMap, goodsIdMap);

        // 清空购物车
        cartService.clearGoods(submit.getUserId(), cartIdList, TpmBusinessTypeEnums.EXPRESS.getCode());

        log.info("提交订单成功, orderId={}", orderId);
        return orderId;
    }

    /**
     * 验证商品并计算总价
     */
    private BigDecimal validateShopGoodsAndCalculatePrice(List<TpmCart> cartGoodsList, Map<Integer, TpmGoodsFullInfoDTO> goodsIdMap,
                                                          TpmTransportTypeEnums transportType) throws Exception {
        BigDecimal goodsTotalPrice = BigDecimal.ZERO;
        for (TpmCart cart : cartGoodsList) {
            TpmGoodsFullInfoDTO goodsFullInfoDTO = goodsIdMap.get(cart.getGoodsId());
            TpmGoods goods = goodsFullInfoDTO.getTpmGoods();
            if (goods == null || !goods.getIsOnSale()) {
                throw new Exception("商品:" + cart.getGoodsName() + "已下架");
            }
            if (goods.getIsSellOut() || goods.getInventoryNum() <= 0) {
                throw new Exception("商品:" + goods.getName() + "已售罄");
            }
            if (!Objects.equals(goods.getTransportType(), transportType.getCode())) {
                throw new Exception("商品:" + goods.getName() + "不支持" + transportType.getDesc() + "配送");
            }
            List<TpmGoodsAttributesDto> cartAttributes = new ArrayList<>();
            if (StringUtils.isNoneBlank(cart.getSpecifications())) {
                cartAttributes = JSONArray.parseArray(cart.getSpecifications(), TpmGoodsAttributesDto.class);
            }
            List<TpmGoodsAttributesDto> tpmGoodsAttributesDtos = goodsFullInfoDTO.getGoodsAttributes();
            // 比较属性
            String validateResult = attributeSpecificationService.attributesSpecificationValid(cartAttributes, tpmGoodsAttributesDtos);
            if (StringUtils.isNoneBlank(validateResult)) {
                throw new Exception(validateResult);
            }
            BigDecimal specificationsPrice = BigDecimal.ZERO;
            TpmSpecificationsResultDto specificationsResultDto = attributeSpecificationService.getSelectedSpecificationsResult(cartAttributes);
            if (specificationsResultDto != null) {
                specificationsPrice = specificationsResultDto.getSpecificationTotalPrice();
            }
            BigDecimal price = goods.getRetailPrice().add(specificationsPrice); // 此处简化，可加入规格价格逻辑
            goodsTotalPrice = goodsTotalPrice.add(price.multiply(BigDecimal.valueOf(cart.getNumber())));
        }
        return goodsTotalPrice;
    }

    /**
     * 创建订单
     */
    private TpmOrder createShopOrder(TpmOrderSubmitReqVo submit, TpmAddress address, BigDecimal goodsTotalPrice, TpmTransportTypeEnums transportType) {
        TpmOrder order = new TpmOrder();
        BigDecimal freightPrice = submit.getFreightPrice() == null ? BigDecimal.ZERO : submit.getFreightPrice();
        BigDecimal packingFee = submit.getPackingFee() == null ? BigDecimal.ZERO : submit.getPackingFee();
        BigDecimal orderTotalPrice = goodsTotalPrice.add(freightPrice).add(packingFee);
        order.setUserId(submit.getUserId());
        order.setOrderSn(orderService.generateOrderSn(submit.getUserId()));
        order.setOrderStatus(OrderUtil.STATUS_CREATE);
        order.setMessage(submit.getRemark());
        order.setConsignee(address.getName());
        order.setMobile(address.getMobile());
        order.setAddress(detailedAddress(address));
        order.setGoodsPrice(goodsTotalPrice);
        order.setFreightPrice(freightPrice);
        order.setPackingFee(packingFee);
        order.setCouponPrice(BigDecimal.ZERO);
        order.setIntegralPrice(BigDecimal.ZERO);
        order.setOrderPrice(orderTotalPrice);
        order.setActualPrice(orderTotalPrice);
        order.setGrouponPrice(BigDecimal.ZERO);
        order.setBusinessType(TpmBusinessTypeEnums.EXPRESS.getCode());
        order.setMealQrCode(UUID.randomUUID().toString().replaceAll("-", ""));
        OrderFreightTypeEnum orderFreightTypeEnum = OrderFreightTypeEnum.convertTransPortType(transportType.getCode());
        if (Objects.nonNull(orderFreightTypeEnum)) {
            order.setFreightType(orderFreightTypeEnum.getValue());
        }
        return order;
    }

    /**
     * 添加订单商品并扣减库存
     */
    private void addOrderGoodsAndReduceInventory(int orderId, List<TpmCart> cartGoodsList,
                                                 Map<Integer, String> cartIdRemarkMap, Map<Integer, TpmGoodsFullInfoDTO> goodsIdMap) throws Exception {
        for (TpmCart cart : cartGoodsList) {
            TpmOrderGoods orderGoods = new TpmOrderGoods();
            orderGoods.setOrderId(orderId);
            orderGoods.setGoodsId(cart.getGoodsId());
            orderGoods.setGoodsName(cart.getGoodsName());
            orderGoods.setPrice(cart.getPrice());
            orderGoods.setNumber(cart.getNumber());
            orderGoods.setPicUrl(cart.getPicUrl());
            orderGoods.setSpecifications(cart.getSpecifications());
            orderGoods.setRemark(cartIdRemarkMap.get(cart.getId()));
            orderGoods.setAddTime(LocalDateTime.now());
            orderGoodsService.add(orderGoods);

            if (tpmGoodsService.reduceInventory(cart.getGoodsId(), cart.getNumber()) == 0) {
                throw new Exception("商品:" + cart.getGoodsName() + "库存扣减失败");
            }
        }
    }

    public String generatePickupCode(Byte freightType) {
        String prefix;
        if (Objects.equals(freightType, (byte) 0)) {
            prefix = "W";
        } else if (Objects.equals(freightType, (byte) 1)) {
            prefix = "Z";
        } else if (Objects.equals(freightType, (byte) 2)) {
            prefix = "S";
        } else {
            throw new IllegalArgumentException("Invalid order type");
        }

        // 获取当天该类型订单的数量
        try {
            int count = getOrderCountForTodayAndType(freightType);

            return prefix + String.format("%03d", count + 1);
        } catch (Exception e) {
            log.error("获取当天订单数量失败", e);
        }
        return prefix + String.format("%03d", RandomUtils.nextInt(100, 999) + 1);

    }

    private int getOrderCountForTodayAndType(Byte freightType) {
        int count = orderService.countByTypeToday(freightType);
        return count;
    }

    /**
     * 取消订单
     * <p>
     * 1. 检测当前订单是否能够取消； 2. 设置订单取消状态； 3. 商品货品库存恢复； 4. TODO 优惠券； 5. TODO 团购活动。
     *
     * @param userId 用户ID
     * @param body   订单信息，{ orderId：xxx }
     * @return 取消订单操作结果
     */
    @Transactional
    public WxResult<String> cancel(TpmOrderOperateReqVo reqVo) {
        Integer userId = reqVo.getUserId();
        if (userId == null) {
            log.error("取消订单失败：用户未登录!");
            return WxResult.unlogin();
        }
        Integer orderId = reqVo.getOrderId();
        if (orderId == null) {
            return WxResult.badArgument();
        }

        TpmOrder order = orderService.findById(orderId);
        if (order == null) {
            return WxResult.badArgumentValue();
        }
        if (!order.getUserId().equals(userId)) {
            return WxResult.badArgumentValue();
        }

        // 检测是否能够取消
        OrderHandleOption handleOption = OrderUtil.build(order);
        if (!handleOption.isCancel()) {
            log.error("取消订单失败：{}", ORDER_INVALID_OPERATION.desc());
            return WxResult.error(ORDER_INVALID_OPERATION);
        }

        // 设置订单已取消状态
        order.setOrderStatus(OrderUtil.STATUS_CANCEL);
        order.setEndTime(LocalDateTime.now());
        if (orderService.updateWithOptimisticLocker(order) == 0) {
            throw new RuntimeException("更新数据已失效");
        }

        // 商品货品数量增加
        List<TpmOrderGoods> orderGoodsList = orderGoodsService.queryByOid(orderId);
        for (TpmOrderGoods orderGoods : orderGoodsList) {
            Integer goodsId = orderGoods.getGoodsId();
            Short number = orderGoods.getNumber();
            if (tpmGoodsService.addInventory(goodsId, number) == 0) {
                throw new RuntimeException("商品货品库存增加失败");
            }
        }
        if (Objects.equals(order.getBusinessType(), TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())) {
            couponUserService.giveBackCoupon(order.getOrderSn());
            List<TpmOrderGoods> orderGoods = orderGoodsService.queryByOid(order.getId());
            callJuDanKePrint(order, orderGoods, "取消联", "195119");
        }
        log.info("【请求结束】用户取消订单,响应结果:{}", "成功");
        return WxResult.success("取消成功");
    }

    /**
     * 付款订单的预支付会话标识
     * <p>
     * 1. 检测当前订单是否能够付款 2. 微信商户平台返回支付订单ID 3. 设置订单付款状态
     *
     * @param userId 用户ID
     * @param body   订单信息，{ orderId：xxx }
     * @return 支付订单ID
     */
    @Transactional
    public WxResult<WxPayMpOrderResult> prepay(TpmOrderPrePayReqVo tpmOrderPrePayReqVo, HttpServletRequest request) {
        Integer userId = tpmOrderPrePayReqVo.getUserId();
        if (userId == null) {
            log.error("付款订单的预支付会话标识失败：用户未登录!");
            return WxResult.unlogin();
        }
        Integer orderId = tpmOrderPrePayReqVo.getOrderId();
        if (orderId == null) {
            return WxResult.badArgument();
        }

        TpmOrder order = orderService.findById(orderId);
        if (order == null) {
            return WxResult.badArgumentValue();
        }
        if (!order.getUserId().equals(userId)) {
            return WxResult.badArgumentValue();
        }

        // 检测是否能够支付
        OrderHandleOption handleOption = OrderUtil.build(order);
        if (!handleOption.isPay()) {
            log.error("付款订单的预支付会话标识失败：{}", ORDER_REPAY_OPERATION.desc());
            return WxResult.error(ORDER_REPAY_OPERATION);
        }

        TpmUser user = userService.findById(userId);
        String openid = user.getWeixinOpenid();
        if (openid == null) {
            log.error("付款订单的预支付会话标识失败：{}", AUTH_OPENID_UNACCESS.desc());
            return WxResult.error(AUTH_OPENID_UNACCESS);
        }
        WxPayMpOrderResult result = null;
        try {
            WxPayUnifiedOrderRequest orderRequest = new WxPayUnifiedOrderRequest();
            orderRequest.setOutTradeNo(order.getOrderSn());
            orderRequest.setOpenid(openid);
            orderRequest.setBody(CommConsts.DEFAULT_ORDER_FIX + order.getOrderSn());
            // 元转成分
            int fee = 0;
            BigDecimal actualPrice = order.getActualPrice();
            fee = actualPrice.multiply(new BigDecimal(100)).intValue();
            orderRequest.setTotalFee(fee);
            orderRequest.setSpbillCreateIp(IpUtil.getIpAddr(request));

            result = wxPayService.createOrder(orderRequest);

            // 缓存prepayID用于后续模版通知
            String prepayId = result.getPackageValue();
            prepayId = prepayId.replace("prepay_id=", "");
            TpmUserFormid userFormid = new TpmUserFormid();
            userFormid.setOpenid(user.getWeixinOpenid());
            userFormid.setFormid(prepayId);
            userFormid.setIsprepay(true);
            userFormid.setUseamount(3);
            userFormid.setExpireTime(LocalDateTime.now().plusDays(7));
            formIdService.addUserFormid(userFormid);

        } catch (Exception e) {
            log.error("付款订单的预支付会话标识失败：{}", ORDER_PAY_FAIL.desc());
            e.printStackTrace();
            return WxResult.error(ORDER_PAY_FAIL);
        }

        order.setPayType(OrderPayTypeEnums.WECHAT.getCode());

        if (orderService.updateWithOptimisticLocker(order) == 0) {
            log.error("付款订单的预支付会话标识失败：{}", "更新订单信息失败");
            return WxResult.updatedDateExpired();
        }

        log.info("【请求结束】购物车商品删除成功:清理的productIds:{}", JSONObject.toJSONString(result));
        return WxResult.success(result);
    }

    /**
     * 走传统的合并下单，支付金额为两个订单的总和
     *
     * @param tpmOrderCombinePrePayReqVo
     * @param request
     * @return
     */
    public WxResult<WxPayMpOrderResult> combinePrepayV2(TpmOrderCombinePrePayReqVo tpmOrderCombinePrePayReqVo, HttpServletRequest request) {
        log.info("【请求开始】合并订单的预支付会话标识,请求参数 reqVo:{}", JSONObject.toJSONString(tpmOrderCombinePrePayReqVo));
        Integer userId = tpmOrderCombinePrePayReqVo.getUserId();
        if (userId == null) {
            log.error("付款订单的预支付会话标识失败：用户未登录!");
            return WxResult.unlogin();
        }
        Integer coldChainOrderId = tpmOrderCombinePrePayReqVo.getColdChainOrderId();
        if (coldChainOrderId == null) {
            log.error("冷链订单id为空");
            return WxResult.error("冷链订单id为空");
        }
        TpmOrder coldChainOrder = orderService.findById(coldChainOrderId);
        if (coldChainOrder == null) {
            log.error("冷链订单不存在");
            return WxResult.error("冷链订单不存在");
        }
        if (!orderAllowPay(coldChainOrder)) {
            return WxResult.error(ORDER_CANT_PAY);
        }
        Integer commonOrderId = tpmOrderCombinePrePayReqVo.getCommonOrderId();
        if (commonOrderId == null) {
            log.error("普快订单id为空");
        }
        TpmOrder commonOrder = orderService.findById(commonOrderId);
        if (commonOrder == null) {
            log.error("普快订单不存在");
        }
        if (!orderAllowPay(commonOrder)) {
            return WxResult.error(ORDER_CANT_PAY);
        }
        TpmUser user = userService.findById(userId);
        String openid = user.getWeixinOpenid();
        if (openid == null) {
            log.error("付款订单的预支付会话标识失败：{}", AUTH_OPENID_UNACCESS.desc());
        }
        WxPayMpOrderResult result = null;
        try {
            WxPayUnifiedOrderRequest orderRequest = new WxPayUnifiedOrderRequest();
            orderRequest.setOutTradeNo(coldChainOrder.getOrderSn() + "-" + commonOrder.getOrderSn());
            orderRequest.setOpenid(openid);
            orderRequest.setBody(CommConsts.DEFAULT_ORDER_FIX + coldChainOrder.getOrderSn() + "-" + commonOrder.getOrderSn());
            // 元转成分
            int fee = 0;
            BigDecimal coldChainActualPrice = coldChainOrder.getActualPrice();
            BigDecimal commonActualPrice = commonOrder.getActualPrice();
            fee = (coldChainActualPrice.add(commonActualPrice)).multiply(new BigDecimal(100)).intValue();
            orderRequest.setTotalFee(fee);
            orderRequest.setSpbillCreateIp(IpUtil.getIpAddr(request));

            result = wxPayService.createOrder(orderRequest);

            // 缓存prepayID用于后续模版通知
            String prepayId = result.getPackageValue();
            prepayId = prepayId.replace("prepay_id=", "");
            TpmUserFormid userFormid = new TpmUserFormid();
            userFormid.setOpenid(user.getWeixinOpenid());
            userFormid.setFormid(prepayId);
            userFormid.setIsprepay(true);
            userFormid.setUseamount(3);
            userFormid.setExpireTime(LocalDateTime.now().plusDays(7));
            formIdService.addUserFormid(userFormid);

        } catch (Exception e) {
            log.error("付款订单的预支付会话标识失败：{}", ORDER_PAY_FAIL.desc());
            e.printStackTrace();
            return WxResult.error(ORDER_PAY_FAIL);
        }

        coldChainOrder.setPayType(OrderPayTypeEnums.WECHAT.getCode());
        commonOrder.setPayType(OrderPayTypeEnums.WECHAT.getCode());

        if (orderService.updateWithOptimisticLocker(coldChainOrder) == 0) {
            log.error("付款订单的预支付会话标识失败：{}", "更新订单信息失败");
            return WxResult.updatedDateExpired();
        }
        if (orderService.updateWithOptimisticLocker(commonOrder) == 0) {
            log.error("付款订单的预支付会话标识失败：{}", "更新订单信息失败");
            return WxResult.updatedDateExpired();
        }

        log.info("【请求结束】购物车商品删除成功:清理的productIds:{}", JSONObject.toJSONString(result));
        return WxResult.success(result);
    }

    public boolean orderAllowPay(TpmOrder tpmOrder) {
        // 检测是否能够支付
        OrderHandleOption handleOption = OrderUtil.build(tpmOrder);
        if (!handleOption.isPay()) {
            log.error("付款订单的预支付会话标识失败：{}", ORDER_REPAY_OPERATION.desc());
            return false;
        }
        return true;
    }

    @Transactional
    public WxResult<CombineTransactionsResult> combinePrepay(TpmOrderCombinePrePayReqVo
                                                                     combinePrePayReqVo, HttpServletRequest request) {
//        Integer userId = combinePrePayReqVo.getUserId();
//        if (userId == null) {
//            log.error("付款订单的预支付会话标识失败：用户未登录!");
//            return WxResult.unlogin();
//        }
//        List<Integer> orderIdList = combinePrePayReqVo.getOrderIdList();
//        if (CollectionUtils.isEmpty(orderIdList)) {
//            return WxResult.badArgument();
//        }
//
//        List<TpmOrder> tpmOrderList = orderService.findById(orderIdList);
//        if (CollectionUtils.isEmpty(tpmOrderList)) {
//            return WxResult.badArgumentValue();
//        }
//        if (tpmOrderList.stream().anyMatch(order -> !order.getUserId().equals(userId))) {
//            return WxResult.badArgumentValue();
//        }
//        if (tpmOrderList.size() != 2) {
//            return WxResult.error("只允许两单合并支付");
//        }
//        // 检测是否能够支付
//        for (TpmOrder order : tpmOrderList) {
//            OrderHandleOption handleOption = OrderUtil.build(order);
//            if (!handleOption.isPay()) {
//                log.error("订单状态不允许支付");
//                return WxResult.error("订单状态不允许支付");
//            }
//        }
//
//        TpmUser user = userService.findById(userId);
//        String openid = user.getWeixinOpenid();
//        if (openid == null) {
//            log.error("付款订单的预支付会话标识失败：{}", AUTH_OPENID_UNACCESS.desc());
//            return WxResult.error(AUTH_OPENID_UNACCESS);
//        }
//        TpmOrder tpmOrder1 = tpmOrderList.get(0);
//        TpmOrder tpmOrder2 = tpmOrderList.get(1);
//        CombineTransactionsResult result = null;
//        try {
//            CombineTransactionsRequest combineTransactionsRequest = new CombineTransactionsRequest();
//            combineTransactionsRequest.setCombineOutTradeNo("COT" + tpmOrder1.getId() + "-" + tpmOrder2.getId());
//            WxPayConfig config = wxPayService.getConfig();
//            List<CombineTransactionsRequest.SubOrders> subOrderList = getSubOrderList(tpmOrder1, tpmOrder2, config);
//
//            combineTransactionsRequest.setSubOrders(subOrderList);
//            CombineTransactionsRequest.CombinePayerInfo combinePayerInfo = new CombineTransactionsRequest.CombinePayerInfo();
//            combinePayerInfo.setOpenid(user.getWeixinOpenid());
//            combineTransactionsRequest.setCombinePayerInfo(combinePayerInfo);
//            combineTransactionsRequest.setNotifyUrl(config.getNotifyUrl() + "/combine");
//            result = wxPayService.combine(TradeTypeEnum.JSAPI, combineTransactionsRequest);
//        } catch (Exception e) {
//            log.error("付款订单的预支付会话标识失败：{}", ORDER_PAY_FAIL.desc());
//            e.printStackTrace();
//            return WxResult.error(ORDER_PAY_FAIL);
//        }
//
//        tpmOrder1.setPayType(OrderPayTypeEnums.WECHAT.getCode());
//        tpmOrder2.setPayType(OrderPayTypeEnums.WECHAT.getCode());
//
//        if (orderService.updateWithOptimisticLocker(tpmOrder1) == 0) {
//            log.error("付款订单的预支付会话标识失败：{}", "更新订单信息失败");
//            return WxResult.updatedDateExpired();
//        }
//        if (orderService.updateWithOptimisticLocker(tpmOrder2) == 0) {
//            log.error("付款订单的预支付会话标识失败：{}", "更新订单信息失败");
//            return WxResult.updatedDateExpired();
//        }
//
//        log.info("【请求结束】购物车商品删除成功:清理的productIds:{}", JSONObject.toJSONString(result));
//        return WxResult.success(result);
        return WxResult.error("暂不支持");
    }


    private List<CombineTransactionsRequest.SubOrders> getSubOrderList(TpmOrder tpmOrder1, TpmOrder
            tpmOrder2, WxPayConfig config) {
        List<CombineTransactionsRequest.SubOrders> subOrders = new ArrayList<>();

        CombineTransactionsRequest.SubOrders subOrders1 = new CombineTransactionsRequest.SubOrders();
        subOrders1.setMchid(config.getMchId());
        subOrders1.setAttach(tpmOrder1.getOrderSn());
        CombineTransactionsRequest.Amount amount1 = new CombineTransactionsRequest.Amount();
        amount1.setTotalAmount(tpmOrder1.getActualPrice().multiply(BigDecimal.valueOf(100)).intValue());
        amount1.setCurrency("CNY");
        subOrders1.setAmount(amount1);
        subOrders1.setOutTradeNo(tpmOrder1.getOrderSn());
        subOrders1.setDescription(tpmOrder1.getOrderSn());
        CombineTransactionsRequest.SubOrders subOrders2 = new CombineTransactionsRequest.SubOrders();
        subOrders2.setMchid(config.getMchId());
        subOrders2.setAttach(tpmOrder2.getOrderSn());
        CombineTransactionsRequest.Amount amount2 = new CombineTransactionsRequest.Amount();
        amount2.setTotalAmount(tpmOrder2.getActualPrice().multiply(BigDecimal.valueOf(100)).intValue());
        amount2.setCurrency("CNY");
        subOrders2.setAmount(amount2);
        subOrders2.setOutTradeNo(tpmOrder2.getOrderSn());
        subOrders2.setDescription(tpmOrder2.getOrderSn());
        subOrders.add(subOrders1);
        subOrders.add(subOrders2);
        return subOrders;
    }


    private static final ExecutorService executorService = Executors.newFixedThreadPool(5);


    /**
     * 微信付款成功或失败回调接口
     * <p>
     * 1. 检测当前订单是否是付款状态; 2. 设置订单付款成功状态相关信息; 3. 响应微信商户平台.
     *
     * @param request  请求内容
     * @param response 响应内容
     * @return 操作结果
     */
    @Transactional
    public String tpmPayNotify(HttpServletRequest request, HttpServletResponse response) {
        String xmlResult;
        try {
            xmlResult = IOUtils.toString(request.getInputStream(), request.getCharacterEncoding());
        } catch (IOException e) {
            log.error("微信付款成功或失败回调失败：{}", "获取回调消息内容错误!", e);
            return WxPayNotifyResponse.fail(e.getMessage());
        }
        WxPayOrderNotifyResult result;
        try {
            result = wxPayService.parseOrderNotifyResult(xmlResult);
        } catch (WxPayException e) {
            log.error("微信付款成功或失败回调失败：{}", "格式化消息内容错误!", e);
            return WxPayNotifyResponse.fail(e.getMessage());
        }
        log.info("处理腾讯支付平台的订单支付：{}", JSONObject.toJSONString(result));
        String orderSn = result.getOutTradeNo();
        String payId = result.getTransactionId();
        // 分转化成元
        String totalFee = BaseWxPayResult.fenToYuan(result.getTotalFee());
        if (orderSn.contains("-")) {
            return handleCombinePay(result, orderSn, payId, totalFee);
        }
        TpmOrder order = orderService.findBySn(orderSn);
        if (order == null) {
            log.error("微信付款成功或失败回调失败：{}", "订单不存在 sn=" + orderSn);
            return WxPayNotifyResponse.fail("订单不存在 sn=" + orderSn);
        }
        // 检查这个订单是否已经处理过
        if (OrderUtil.isPayStatus(order) && order.getPayId() != null) {
            log.warn("警告：微信付款成功或失败回调：{}", "订单已经处理成功!");
            return WxPayNotifyResponse.success("订单已经处理成功!");
        }
        // 检查支付订单金额
        if (!totalFee.equals(order.getActualPrice().toString())) {
            log.error("微信付款成功或失败回调失败：{}", order.getOrderSn() + " : 支付金额不符合 totalFee=" + totalFee);
            return WxPayNotifyResponse.fail(order.getOrderSn() + " : 支付金额不符合 totalFee=" + totalFee);
        }
        order.setPayId(payId);
        order.setPayTime(LocalDateTime.now());
        order.setOrderStatus(OrderUtil.STATUS_PAY);
        if (orderService.updateWithOptimisticLocker(order) == 0) {
            // 这里可能存在这样一个问题，用户支付和系统自动取消订单发生在同时
            // 如果数据库首先因为系统自动取消订单而更新了订单状态；
            // 此时用户支付完成回调这里也要更新数据库，而由于乐观锁机制这里的更新会失败
            // 因此，这里会重新读取数据库检查状态是否是订单自动取消，如果是则更新成支付状态。
            order = orderService.findBySn(orderSn);
            int updated = 0;
            if (OrderUtil.isAutoCancelStatus(order)) {
                order.setPayId(payId);
                order.setPayTime(LocalDateTime.now());
                order.setOrderStatus(OrderUtil.STATUS_PAY);
                updated = orderService.updateWithOptimisticLocker(order);
            }
            // 如果updated是0，那么数据库更新失败
            if (updated == 0) {
                return WxPayNotifyResponse.fail("更新数据已失效");
            }
        }

        if (Objects.equals(order.getBusinessType(), TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())) {
            //订单支付企业微信提醒
            List<TpmOrderGoods> orderGoods = orderGoodsService.queryByOid(order.getId());
            TpmCouponCodeUseLog tpmCouponCodeUseLog = tpmCouponCodeUseLogService.findByOrderId(order.getId());
            TpmOrder finalOrder = order;
            executorService.submit(() -> {
                //订单支付企业微信提醒
                orderPaidWxNotify(finalOrder);
                pushOrder2JuDanKe(finalOrder, orderGoods);
                callJuDanKePrint(finalOrder, orderGoods, tpmCouponCodeUseLog);
            });
        } else if (Objects.equals(order.getBusinessType(), TpmBusinessTypeEnums.EXPRESS.getCode())) {
            //订单支付企业微信提醒
            orderPaidShopWxNotify(order);
            printMallOrderKitchen(order);
        }


        // TODO 发送邮件和短信通知，这里采用异步发送
        // 订单支付成功以后，会发送短信给用户，以及发送邮件给管理员
        // notifyService.notifyMail("新订单通知", order.toString());
//        notifyService.notifySslMail("新订单通知", OrderUtil.orderHtmlText(order, orderIds, orderGoodsList));
        // 这里微信的短信平台对参数长度有限制，所以将订单号只截取后6位，暂时屏蔽，因为已有微信模板提醒
        // notifyService.notifySmsTemplateSync(order.getMobile(),
        // NotifyType.PAY_SUCCEED, new String[]{orderSn.substring(8, 14)});

        // 请依据自己的模版消息配置更改参数
//        String[] parms = new String[]{order.getOrderSn(), order.getOrderPrice().toString(),
//                DateTimeUtil.getDateTimeDisplayString(order.getAddTime()), order.getConsignee(), order.getMobile(),
//                order.getAddress()};

        // notifyService.notifyWxTemplate(result.getOpenid(), NotifyType.PAY_SUCCEED,
        // parms, "pages/index/index?orderId=" + order.getId());
//        notifyService.notifyWxTemplate(result.getOpenid(), NotifyType.PAY_SUCCEED, parms, "/pages/ucenter/order/order");

        log.info("【请求结束】微信付款成功或失败回调:响应结果:{}", "处理成功!");
        return WxPayNotifyResponse.success("处理成功!");
    }

    private String handleCombinePay(WxPayOrderNotifyResult result, String orderSn, String payId, String totalFee) {
        String[] split = orderSn.split("-");
        String coldChainOrderSn = split[0];
        String commonOrderSn = split[1];
        List<TpmOrder> orderList = orderService.findBySn(Arrays.asList(coldChainOrderSn, commonOrderSn));
        if (CollectionUtils.isEmpty(orderList)) {
            log.error("微信付款成功或失败回调失败：{}", "订单不存在 sn=" + orderSn);
            return WxPayNotifyResponse.fail("订单不存在 sn=" + orderSn);
        }
        if (orderList.size() != 2) {
            log.error("微信付款成功或失败回调失败：{}", "订单不存在 sn=" + orderSn);
            return WxPayNotifyResponse.fail("订单不存在 sn=" + orderSn);
        }
        BigDecimal allActualPrice = orderList.stream().map(TpmOrder::getActualPrice).reduce(BigDecimal::add).orElse(null);
        // 检查支付订单金额
        if (!totalFee.equals(allActualPrice.toString())) {
            log.error("微信付款成功或失败回调失败：{}", "支付金额不符合 totalFee=" + totalFee);
            return WxPayNotifyResponse.fail("支付金额不符合 totalFee=" + totalFee);
        }
        for (TpmOrder order : orderList) {
            if (order == null) {
                log.error("微信付款成功或失败回调失败：{}", "订单不存在 sn=" + orderSn);
                return WxPayNotifyResponse.fail("订单不存在 sn=" + orderSn);
            }
            // 检查这个订单是否已经处理过
            if (OrderUtil.isPayStatus(order) && order.getPayId() != null) {
                log.warn("警告：微信付款成功或失败回调：{}", "订单已经处理成功!");
                return WxPayNotifyResponse.success("订单已经处理成功!");
            }

            order.setPayId(payId);
            order.setPayTime(LocalDateTime.now());
            order.setOrderStatus(OrderUtil.STATUS_PAY);
            if (orderService.updateWithOptimisticLocker(order) == 0) {
                // 这里可能存在这样一个问题，用户支付和系统自动取消订单发生在同时
                // 如果数据库首先因为系统自动取消订单而更新了订单状态；
                // 此时用户支付完成回调这里也要更新数据库，而由于乐观锁机制这里的更新会失败
                // 因此，这里会重新读取数据库检查状态是否是订单自动取消，如果是则更新成支付状态。
                order = orderService.findBySn(orderSn);
                int updated = 0;
                if (OrderUtil.isAutoCancelStatus(order)) {
                    order.setPayId(payId);
                    order.setPayTime(LocalDateTime.now());
                    order.setOrderStatus(OrderUtil.STATUS_PAY);
                    updated = orderService.updateWithOptimisticLocker(order);
                }
                // 如果updated是0，那么数据库更新失败
                if (updated == 0) {
                    return WxPayNotifyResponse.fail("更新数据已失效");
                }
            }
            List<TpmOrderGoods> orderGoods = orderGoodsService.queryByOid(order.getId());
            TpmOrder finalOrder = order;
            executorService.submit(() -> {
                //订单支付企业微信提醒
                shopOrderPaidWxNotify(finalOrder);
                printMallOrderKitchen(finalOrder);
            });
        }
        return WxPayNotifyResponse.success("处理成功!");
    }

    /**
     * 微信付款成功或失败回调接口
     * <p>
     * 1. 检测当前订单是否是付款状态; 2. 设置订单付款成功状态相关信息; 3. 响应微信商户平台.
     *
     * @param request  请求内容
     * @param response 响应内容
     * @return 操作结果
     */
    @Transactional
    public String tpmCombinePayNotify(HttpServletRequest request, HttpServletResponse response) {
        String xmlResult;
        try {
            xmlResult = IOUtils.toString(request.getInputStream(), request.getCharacterEncoding());
        } catch (IOException e) {
            log.error("微信付款成功或失败回调失败：{}", "获取回调消息内容错误!", e);
            return WxPayNotifyResponse.fail(e.getMessage());
        }
        CombineNotifyResult result;
        try {
            result = wxPayService.parseCombineNotifyResult(xmlResult, null);
        } catch (WxPayException e) {
            log.error("微信付款成功或失败回调失败：{}", "格式化消息内容错误!", e);
            return WxPayNotifyResponse.fail(e.getMessage());
        }
        log.info("处理腾讯支付平台的订单支付：{}", JSONObject.toJSONString(result));
        CombineNotifyResult.DecryptNotifyResult decryptNotifyResult = result.getResult();
        String combineOutTradeNo = decryptNotifyResult.getCombineOutTradeNo();
        List<CombineNotifyResult.SubOrders> subOrders = decryptNotifyResult.getSubOrders();
        for (CombineNotifyResult.SubOrders subOrder : subOrders) {
            String payId = subOrder.getTransactionId();
            String orderSn = subOrder.getOutTradeNo();
            // 分转化成元
            String totalFee = BaseWxPayResult.fenToYuan(subOrder.getAmount().getTotalAmount());
            TpmOrder order = orderService.findBySn(orderSn);
            if (order == null) {
                log.error("微信付款成功或失败回调失败：{}", "订单不存在 sn=" + orderSn);
                return WxPayNotifyResponse.fail("订单不存在 sn=" + orderSn);
            }
            // 检查这个订单是否已经处理过
            if (OrderUtil.isPayStatus(order) && order.getPayId() != null) {
                log.warn("警告：微信付款成功或失败回调：{}", "订单已经处理成功!");
                return WxPayNotifyResponse.success("订单已经处理成功!");
            }
            // 检查支付订单金额
            if (!totalFee.equals(order.getActualPrice().toString())) {
                log.error("微信付款成功或失败回调失败：{}", order.getOrderSn() + " : 支付金额不符合 totalFee=" + totalFee);
                return WxPayNotifyResponse.fail(order.getOrderSn() + " : 支付金额不符合 totalFee=" + totalFee);
            }
            order.setPayId(payId);
            order.setPayTime(LocalDateTime.now());
            order.setOrderStatus(OrderUtil.STATUS_PAY);
            if (orderService.updateWithOptimisticLocker(order) == 0) {
                // 这里可能存在这样一个问题，用户支付和系统自动取消订单发生在同时
                // 如果数据库首先因为系统自动取消订单而更新了订单状态；
                // 此时用户支付完成回调这里也要更新数据库，而由于乐观锁机制这里的更新会失败
                // 因此，这里会重新读取数据库检查状态是否是订单自动取消，如果是则更新成支付状态。
                order = orderService.findBySn(orderSn);
                int updated = 0;
                if (OrderUtil.isAutoCancelStatus(order)) {
                    order.setPayId(payId);
                    order.setPayTime(LocalDateTime.now());
                    order.setOrderStatus(OrderUtil.STATUS_PAY);
                    updated = orderService.updateWithOptimisticLocker(order);
                }
                // 如果updated是0，那么数据库更新失败
                if (updated == 0) {
                    return WxPayNotifyResponse.fail("更新数据已失效");
                }
            }
            TpmOrder finalOrder = order;
            executorService.submit(() -> {
                //订单支付企业微信提醒
                orderPaidWxNotify(finalOrder);
                printMallOrderKitchen(finalOrder);
            });

        }

        log.info("【请求结束】微信付款成功或失败回调:响应结果:{}", "处理成功!");
        return WxPayNotifyResponse.success("处理成功!");
    }

    private void printMallOrderKitchen(TpmOrder tpmOrder) {
        try {
            printService.mallPrint(Arrays.asList(tpmOrder.getId()), Arrays.asList(CommConsts.ORDER_DELIVERY, CommConsts.ORDER_KITCHEN));
        } catch (Exception e) {
            log.error("打印备货单失败 error={}", e.getMessage(), e);
            WechatNotifyUtils.wechatNotifyMd(SystemConfig.getWechatShopNotifyUrl(), null, "打印备货单失败 订单号:" + tpmOrder.getOrderSn());
        }
    }

    private String pushOrder2JuDanKe(TpmOrder order, List<TpmOrderGoods> orderGoods) {
        try {
            if (Objects.equals(order.getFreightType(), (byte) 0) && SystemConfig.getAutoPushJDK()) {
                log.info("订单:{} 自动推送聚单客", order.getOrderSn());
                // 查询表体
                String deliveryOrderId = jdkOrderService.preCreateOrder(order, orderGoods);
                log.info("订单:{} 自动推送聚单客 orderId:{}", order.getOrderSn(), deliveryOrderId);
                jdkOrderService.newThirdQuotationAndThirdPublish(order, Long.valueOf(deliveryOrderId));
                log.info("订单:{} 自动发单 orderId:{}", order.getOrderSn(), deliveryOrderId);
                TpmOrder tpmOrder = orderService.findById(order.getId());
                tpmOrder.setDeliveryOrderId(deliveryOrderId);
                tpmOrder.setOrderStatus(OrderUtil.STATUS_SHIP);
                tpmOrder.setShipTime(LocalDateTime.now());
                orderService.updateWithOptimisticLocker(tpmOrder);
                return deliveryOrderId;
            }
        } catch (Exception e) {
            log.error("第三方配送单推送失败", e);
        }
        return null;
    }

    private void callJuDanKePrint(TpmOrder order, List<TpmOrderGoods> orderGoods, String stickerType, String
            templateId) {
        try {
            jdkOrderService.printerPrint(order, orderGoods, stickerType, templateId);
        } catch (Exception e) {
            log.error("打印小票失败", e);
        }
    }

    private void callJuDanKePrint(TpmOrder order, List<TpmOrderGoods> orderGoods, TpmCouponCodeUseLog
            tpmCouponCodeUseLog) {
        try {
//            jdkOrderService.printerPrint(order, orderGoods, "后厨联", "195121");
            jdkOrderService.printerPrint(order, orderGoods, "商家联", "195118", tpmCouponCodeUseLog);
            jdkOrderService.printerPrint(order, orderGoods, "顾客联", "195117", tpmCouponCodeUseLog);
        } catch (Exception e) {
            WechatNotifyUtils.wechatNotifyMd(SystemConfig.getWechatNotifyUrl(), null, "订单:" + order.getOrderSn() + "小票打印失败，请及时处理");
            log.error("打印小票失败", e);
        }
    }


    private static void orderPaidWxNotify(TpmOrder order) {
        String notifyMessage = "您有新的订单,订单号为:" + order.getOrderSn();
        if (Objects.equals(order.getFreightType(), OrderFreightTypeEnum.Distribution.getValue())) {
            notifyMessage = "您有新的外送订单,订单号为:" + order.getOrderSn();
        } else if (Objects.equals(order.getFreightType(), OrderFreightTypeEnum.Pickup.getValue())) {
            notifyMessage = "您有新的自提订单,订单号为:" + order.getOrderSn();
        } else if (Objects.equals(order.getFreightType(), OrderFreightTypeEnum.DINE_IN.getValue())) {
            notifyMessage = "您有新的堂食订单,订单号为:" + order.getOrderSn();
        } else if (Objects.equals(order.getFreightType(), OrderFreightTypeEnum.COLD_CHAIN.getValue())) {
            notifyMessage = "您有新的冷链订单,订单号为:" + order.getOrderSn();
        } else if (Objects.equals(order.getFreightType(), OrderFreightTypeEnum.COMMON.getValue())) {
            notifyMessage = "您有新的普快订单,订单号为:" + order.getOrderSn();
        }
        if (!StringUtils.isBlank(order.getMessage())) {
            notifyMessage += " 备注:" + order.getMessage();
        }

        try {
            log.info("外卖订单:{} 企微提醒", order.getOrderSn());
            WechatNotifyUtils.wechatNotifyMd(SystemConfig.getWechatNotifyUrl(), null, notifyMessage);
        } catch (Exception e) {
            log.error("订单通知推送失败", e);
        }
    }

    private static void orderPaidShopWxNotify(TpmOrder order) {
        String notifyMessage = "您有新的订单,订单号为:" + order.getOrderSn();
        if (Objects.equals(order.getFreightType(), OrderFreightTypeEnum.COLD_CHAIN.getValue())) {
            notifyMessage = "您有新的冷链订单,订单号为:" + order.getOrderSn();
        } else if (Objects.equals(order.getFreightType(), OrderFreightTypeEnum.COMMON.getValue())) {
            notifyMessage = "您有新的普快订单,订单号为:" + order.getOrderSn();
        }
        if (!StringUtils.isBlank(order.getMessage())) {
            notifyMessage += " 备注:" + order.getMessage();
        }
        try {
            log.info("商城订单:{} 企微提醒", order.getOrderSn());
            WechatNotifyUtils.wechatNotifyMd(SystemConfig.getWechatShopNotifyUrl(), null, notifyMessage);
        } catch (Exception e) {
            log.error("商城订单通知推送失败", e);
        }
    }

    private static void shopOrderPaidWxNotify(TpmOrder order) {
        String notifyMessage = "您有新的订单,订单号为:" + order.getOrderSn();
        if (Objects.equals(order.getFreightType(), OrderFreightTypeEnum.COLD_CHAIN.getValue())) {
            notifyMessage = "您有新的冷链订单,订单号为:" + order.getOrderSn();
        } else if (Objects.equals(order.getFreightType(), OrderFreightTypeEnum.COMMON.getValue())) {
            notifyMessage = "您有新的普快订单,订单号为:" + order.getOrderSn();
        }
        if (!StringUtils.isBlank(order.getMessage())) {
            notifyMessage += " 备注:" + order.getMessage();
        }
        try {
            log.info("商城订单:{} 企微提醒", order.getOrderSn());
            WechatNotifyUtils.wechatNotifyMd(SystemConfig.getWechatShopNotifyUrl(), null, notifyMessage);
        } catch (Exception e) {
            log.error("订单通知推送失败", e);
        }
    }

    /**
     * 将不变的订单属性复制到子订单
     *
     * @param order
     * @param childOrder
     * @return
     */
    private TpmOrder copyfixedOrderAttr(TpmOrder order, TpmOrder childOrder) {
        if (childOrder != null && order != null) {
            childOrder.setAddress(order.getAddress());
            childOrder.setAddTime(order.getAddTime());
            childOrder.setConsignee(order.getConsignee());
            childOrder.setMessage(order.getMessage());
            childOrder.setMobile(order.getMobile());
            childOrder.setUserId(order.getUserId());
        }
        return childOrder;
    }

    /**
     * 将订单中的商品按入驻店铺分离归类
     *
     * @param orderGoodsList
     * @return
     */
    private List<BrandOrderGoods> divideMultiBrandOrderGoods(List<TpmOrderGoods> orderGoodsList) {
        List<BrandOrderGoods> brandOrderGoodsList = new ArrayList<BrandOrderGoods>();
        for (int i = 0; i < orderGoodsList.size(); i++) {
            TpmOrderGoods dog = orderGoodsList.get(i);
            Integer brandId = dog.getBrandId();
            boolean hasExsist = false;
            for (int k = 0; k < brandOrderGoodsList.size(); k++) {
                if (brandOrderGoodsList.get(k).getBrandId().intValue() == dog.getBrandId().intValue()) {
                    brandOrderGoodsList.get(k).getOrderGoodsList().add(dog);
                    hasExsist = true;
                    break;
                }
            }
            if (!hasExsist) { // 还尚未加入，则需要查询品牌入驻商铺
                BrandOrderGoods bog = new BrandOrderGoods();
                bog.setBrandId(brandId);
                List<TpmOrderGoods> childOrderGoodslist = new ArrayList<TpmOrderGoods>();
                childOrderGoodslist.add(dog);
                bog.setOrderGoodsList(childOrderGoodslist);
                brandOrderGoodsList.add(bog);
            }
        }

        return brandOrderGoodsList;
    }

    /**
     * 订单申请退款
     * <p>
     * 1. 检测当前订单是否能够退款； 2. 设置订单申请退款状态。
     *
     * @param userId 用户ID
     * @param body   订单信息，{ orderId：xxx }
     * @return 订单退款操作结果
     */
    @Transactional
    public WxResult<String> refund(TpmOrderOperateReqVo reqVo) {
        Integer userId = reqVo.getUserId();
        if (userId == null) {
            log.error("订单申请退款失败：用户未登录!");
            return WxResult.unlogin();
        }
        Integer orderId = reqVo.getOrderId();
        if (orderId == null) {
            return WxResult.badArgument();
        }

        TpmOrder order = orderService.findById(orderId);
        if (order == null) {
            return WxResult.badArgument();
        }
        if (!order.getUserId().equals(userId)) {
            return WxResult.badArgumentValue();
        }

        OrderHandleOption handleOption = OrderUtil.build(order);
        if (!handleOption.isRefund()) {
            log.error("订单申请退款失败：{}", ORDER_INVALID_OPERATION.desc());
            return WxResult.error(ORDER_INVALID_OPERATION);
        }
        //超过设定的时间不可以取消
        if (Objects.equals(order.getOrderStatus(), OrderUtil.STATUS_PAY)) {
            LocalDateTime payTime = order.getPayTime();
            LocalDateTime currentTime = LocalDateTime.now();
            Duration duration = Duration.between(payTime, currentTime);
            long minutesPassed = duration.toMinutes();
            Integer allowCancelTimeGap = SystemConfig.getAllowCancelTimeGap();
            if (minutesPassed >= allowCancelTimeGap) {
                return WxResult.error("该订单超过取消时间");
            }
        }
        // 设置订单申请退款状态
        order.setOrderStatus(OrderUtil.STATUS_REFUND);
        if (orderService.updateWithOptimisticLocker(order) == 0) {
            log.error("订单申请退款失败：{}", "更新订单信息失败");
            return WxResult.updatedDateExpired();
        }
        String notifyMessage = "您有退款订单需要处理,订单号为:" + order.getOrderSn();
        if (Objects.equals(order.getBusinessType(), TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())) {
            List<TpmOrderGoods> tpmOrderGoods = orderGoodsService.queryByOid(orderId);
            jdkOrderService.printerPrint(order, tpmOrderGoods, "取消联", "195109");
            WechatNotifyUtils.wechatNotifyMd(SystemConfig.getWechatNotifyUrl(), null, notifyMessage);
        } else if (Objects.equals(order.getBusinessType(), TpmBusinessTypeEnums.EXPRESS.getCode())) {
            WechatNotifyUtils.wechatNotifyMd(SystemConfig.getWechatShopNotifyUrl(), null, notifyMessage);
        }
        // TODO 发送邮件和短信通知，这里采用异步发送
        // 有用户申请退款，邮件通知运营人员
        // notifyService.notifyMail("退款申请", order.toString());
//        notifyService.notifySslMail("退款申请", OrderUtil.orderHtmlText(order, order.getId().intValue() + "", null));

        // 请依据自己的模版消息配置更改参数
        /*
         * String[] parms = new String[]{ order.getOrderSn(),
         * order.getOrderPrice().toString(),
         * DateTimeUtil.getDateTimeDisplayString(order.getAddTime()),
         * order.getConsignee(), order.getMobile(), order.getAddress() };
         * notifyService.notifyWxTemplate("oZQrt0N4e5Ps_R-NhtMzsei93-58",
         * NotifyType.APPLYREFUND, parms, "pages/index/index?orderId=" + order.getId());
         */
        log.info("【请求结束】订单申请退款成功！");
        return WxResult.success("申请退款成功");
    }

    /**
     * 确认收货
     * <p>
     * 1. 检测当前订单是否能够确认收货； 2. 设置订单确认收货状态。
     *
     * @param userId 用户ID
     * @param body   订单信息，{ orderId：xxx }
     * @return 订单操作结果
     */
    public WxResult<String> confirm(TpmOrderOperateReqVo reqVo) {
        Integer userId = reqVo.getUserId();
        if (userId == null) {
            log.error("订单确认收货失败：用户未登录!");
            return WxResult.unlogin();
        }
        Integer orderId = reqVo.getOrderId();
        if (orderId == null) {
            return WxResult.badArgument();
        }

        TpmOrder order = orderService.findById(orderId);
        if (order == null) {
            return WxResult.badArgument();
        }
        if (!order.getUserId().equals(userId)) {
            return WxResult.badArgumentValue();
        }

        OrderHandleOption handleOption = OrderUtil.build(order);
        if (!handleOption.isConfirm()) {
            log.error("订单确认收货失败：{}", ORDER_CONFIRM_OPERATION.desc());
            return WxResult.error(ORDER_CONFIRM_OPERATION);
        }

        Short comments = orderGoodsService.getComments(orderId);
        order.setComments(comments);

        order.setOrderStatus(OrderUtil.STATUS_CONFIRM);
        order.setConfirmTime(LocalDateTime.now());
        if (orderService.updateWithOptimisticLocker(order) == 0) {
            log.error("订单确认收货失败：{}", "更新订单信息失败");
            return WxResult.updatedDateExpired();
        }
        TpmPointDto tpmPointDto = new TpmPointDto();
        tpmPointDto.setUserId(userId);
        tpmPointDto.setOrderSn(order.getOrderSn());
        tpmPointDto.setAmount(order.getActualPrice());
        tpmPointDto.setDescription("订单:" + order.getOrderSn() + "确认收货添加积分:" + order.getActualPrice());
        tpmPointService.addPoint(tpmPointDto);
        try {
            if (Objects.equals(order.getPayType(), OrderPayTypeEnums.WECHAT.getCode())) {
                if (Objects.equals(order.getBusinessType(), TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())) {
                    wxUploadShippingInfoUtils.doUploadShippingInfo(orderId);
                } else if (Objects.equals(order.getBusinessType(), TpmBusinessTypeEnums.EXPRESS.getCode())) {
                    wxUploadShippingInfoUtils.doUploadExpressShippingInfo(orderId);
                }
            }
        } catch (Exception e) {
            log.error("订单确认收货失败：{}", e.getMessage());
        }
        log.info("【请求结束】订单确认收货成功！");
        return WxResult.success("订单确认收货成功");
    }

    /**
     * 删除订单
     * <p>
     * 1. 检测当前订单是否可以删除； 2. 删除订单。
     *
     * @param userId 用户ID
     * @param body   订单信息，{ orderId：xxx }
     * @return 订单操作结果
     */
    public WxResult<String> delete(TpmOrderOperateReqVo reqVo) {
        Integer userId = reqVo.getUserId();
        if (userId == null) {
            log.error("删除订单失败：用户未登录!");
            return WxResult.unlogin();
        }
        Integer orderId = reqVo.getOrderId();
        if (orderId == null) {
            return WxResult.badArgument();
        }

        TpmOrder order = orderService.findById(orderId);
        if (order == null) {
            return WxResult.badArgument();
        }
        if (!order.getUserId().equals(userId)) {
            return WxResult.badArgumentValue();
        }

        OrderHandleOption handleOption = OrderUtil.build(order);
        if (!handleOption.isDelete()) {
            log.error("删除订单失败：{}", ORDER_DEL_OPERATION.desc());
            return WxResult.error(ORDER_DEL_OPERATION);
        }

        // 订单order_status没有字段用于标识删除
        // 而是存在专门的delete字段表示是否删除
        orderService.deleteById(orderId);

        log.info("【请求结束】删除订单成功！");
        return WxResult.success("删除订单成功");
    }

    /**
     * 待评价订单商品信息
     *
     * @param userId  用户ID
     * @param orderId 订单ID
     * @param goodsId 商品ID
     * @return 待评价订单商品信息
     */
    public WxResult<TpmOrderGoods> goods(Integer userId, Integer orderId, Integer goodsId) {
        if (userId == null) {
            log.error("获取待评价订单商品订单失败：用户未登录!");
            return WxResult.unlogin();
        }

        List<TpmOrderGoods> orderGoodsList = orderGoodsService.findByOidAndGid(orderId, goodsId);
        int size = orderGoodsList.size();

        Assert.state(size < 2, "存在多个符合条件的订单商品");

        if (size == 0) {
            return WxResult.badArgumentValue();
        }

        TpmOrderGoods orderGoods = orderGoodsList.get(0);

        log.info("【请求结束】获取待评价订单商品订单成功！");
        return WxResult.success(orderGoods);
    }

    /**
     * 评价订单商品
     * <p>
     * 确认商品收货或者系统自动确认商品收货后7天内可以评价，过期不能评价。
     *
     * @param userId 用户ID
     * @param body   订单信息，{ orderId：xxx }
     * @return 订单操作结果
     */
    public WxResult<String> comment(Integer userId, String body) {
        if (userId == null) {
            log.error("评价订单商品失败：用户未登录!");
            return WxResult.unlogin();
        }

        Integer orderGoodsId = JacksonUtil.parseInteger(body, "orderGoodsId");
        if (orderGoodsId == null) {
            return WxResult.badArgument();
        }
        TpmOrderGoods orderGoods = orderGoodsService.findById(orderGoodsId);
        if (orderGoods == null) {
            return WxResult.badArgumentValue();
        }
        Integer orderId = orderGoods.getOrderId();
        TpmOrder order = orderService.findById(orderId);
        if (order == null) {
            return WxResult.badArgumentValue();
        }

        if (!OrderUtil.isConfirmStatus(order) && !OrderUtil.isAutoConfirmStatus(order)) {
            log.error("评价订单商品失败：{}", ORDER_NOT_COMMENT.desc());
            return WxResult.error(ORDER_NOT_COMMENT);
        }
        if (!order.getUserId().equals(userId)) {
            log.error("评价订单商品失败：{}", ORDER_INVALID.desc());
            return WxResult.error(ORDER_INVALID);
        }
        Integer commentId = orderGoods.getComment();
        if (commentId == -1) {
            log.error("评价订单商品失败：{}", ORDER_COMMENT_EXPIRED.desc());
            return WxResult.error(ORDER_COMMENT_EXPIRED);
        }
        if (commentId != 0) {
            log.error("评价订单商品失败：{}", ORDER_COMMENTED.desc());
            return WxResult.error(ORDER_COMMENTED);
        }

        String content = JacksonUtil.parseString(body, "content");
        Integer star = JacksonUtil.parseInteger(body, "star");
        if (star == null || star < 0 || star > 5) {
            return WxResult.badArgumentValue();
        }
        Boolean hasPicture = JacksonUtil.parseBoolean(body, "hasPicture");
        List<String> picUrls = JacksonUtil.parseStringList(body, "picUrls");
        if (hasPicture == null || !hasPicture) {
            picUrls = new ArrayList<>(0);
        }

        // 1. 创建评价
        TpmComment comment = new TpmComment();
        comment.setUserId(userId);
        comment.setType((byte) 0);
        comment.setValueId(orderGoods.getGoodsId());
        comment.setStar(star.shortValue());
        comment.setContent(content);
        comment.setHasPicture(hasPicture);
        comment.setPicUrls(picUrls.toArray(new String[]{}));
        commentService.save(comment);

        // 2. 更新订单商品的评价列表
        orderGoods.setComment(comment.getId());
        orderGoodsService.updateById(orderGoods);

        // 3. 更新订单中未评价的订单商品可评价数量
        Short commentCount = order.getComments();
        if (commentCount > 0) {
            commentCount--;
        }
        order.setComments(commentCount);
        orderService.updateWithOptimisticLocker(order);

        log.info("【请求结束】评价订单商品成功！");
        return WxResult.success("评价成功");
    }

    /**
     * 推广订单列表
     *
     * @param userId   用户代理用户ID
     * @param showType 订单信息： 0，全部订单； 1，有效订单； 2，失效订单； 3，结算订单； 4，待结算订单。
     * @param page     分页页数
     * @param size     分页大小
     * @return 推广订单列表
     */
    public Object settleOrderList(Integer userId, Integer showType, Integer page, Integer size) {
        if (userId == null) {
            log.error("获取推广订单列表失败：用户未登录!");
            return WxResult.unlogin();
        }
        List<Short> orderStatus = OrderUtil.settleOrderStatus(showType);
        List<Short> settlementStatus = OrderUtil.settlementStatus(showType);

        List<TpmOrder> orderList = accountService.querySettlementOrder(userId, orderStatus, settlementStatus, page,
                size);
        long count = PageInfo.of(orderList).getTotal();
        int totalPages = (int) Math.ceil((double) count / size);

        List<Map<String, Object>> orderVoList = new ArrayList<>(orderList.size());
        for (TpmOrder order : orderList) {
            Map<String, Object> orderVo = new HashMap<>();
            orderVo.put("id", order.getId());
            orderVo.put("orderSn", order.getOrderSn());
            orderVo.put("actualPrice", order.getActualPrice());
            orderVo.put("orderStatusText", OrderUtil.orderStatusText(order));
            orderVo.put("handleOption", OrderUtil.build(order));

            List<TpmOrderGoods> orderGoodsList = orderGoodsService.queryByOid(order.getId());
            List<Map<String, Object>> orderGoodsVoList = new ArrayList<>(orderGoodsList.size());
            for (TpmOrderGoods orderGoods : orderGoodsList) {
                Map<String, Object> orderGoodsVo = new HashMap<>();
                orderGoodsVo.put("id", orderGoods.getId());
                orderGoodsVo.put("goodsName", orderGoods.getGoodsName());
                orderGoodsVo.put("number", orderGoods.getNumber());
                orderGoodsVo.put("picUrl", orderGoods.getPicUrl());
                orderGoodsVoList.add(orderGoodsVo);
            }
            orderVo.put("goodsList", orderGoodsVoList);

            orderVoList.add(orderVo);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("count", count);
        result.put("data", orderVoList);
        result.put("totalPages", totalPages);

        log.info("【请求结束】获取推广订单列表成功！,推广订单数：{}", orderVoList.size());
        return WxResult.success(result);
    }

    /**
     * 分页查询取款结算记录
     *
     * @param userId
     * @param page
     * @param size
     * @return
     */
    public Object extractList(Integer userId, Integer page, Integer size) {
        if (userId == null) {
            log.error("分页查询取款结算记录失败：用户未登录!");
            return WxResult.unlogin();
        }

        List<TpmAccountTrace> accountTraceList = accountService.queryAccountTraceList(userId, page, size);
        long count = PageInfo.of(accountTraceList).getTotal();
        int totalPages = (int) Math.ceil((double) count / size);

        Map<String, Object> result = new HashMap<>();
        result.put("count", count);
        result.put("accountTraceList", accountTraceList);
        result.put("totalPages", totalPages);

        log.info("【请求结束】获取佣金提现列表成功！,提现总数：{}", count);
        return WxResult.success(result);
    }

    /**
     * 订单物流跟踪
     *
     * @param userId
     * @param orderId
     * @return
     */
    public Object expressTrace(Integer userId, @NotNull Integer orderId) {
        if (userId == null) {
            log.error("订单物流跟踪失败：用户未登录!");
            return WxResult.unlogin();
        }

        // 订单信息
        TpmOrder order = orderService.findById(orderId);
        if (null == order) {
            log.error("订单物流跟踪失败：{}", ORDER_UNKNOWN.desc());
            return WxResult.error(ORDER_UNKNOWN);
        }
        if (!order.getUserId().equals(userId)) {
            log.error("订单物流跟踪失败：{}", ORDER_INVALID.desc());
            return WxResult.error(ORDER_INVALID);
        }

        Map<String, Object> result = new HashMap<>();
        DateTimeFormatter dateSdf = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        DateTimeFormatter timeSdf = DateTimeFormatter.ofPattern("HH:mm");

        result.put("shipDate", dateSdf.format(order.getShipTime()));
        result.put("shipTime", timeSdf.format(order.getShipTime()));
        result.put("shipperCode", order.getShipSn());
        result.put("address", order.getAddress());

        // "YTO", "800669400640887922"
        if (order.getOrderStatus().equals(OrderUtil.STATUS_SHIP)) {
            ExpressInfo ei = expressService.getExpressInfo(order.getShipChannel(), order.getShipSn());
            if (ei != null) {
                result.put("state", ei.getState());
                result.put("shipperName", ei.getShipperName());
                List<Traces> eiTrace = ei.getTraces();
                List<Map<String, Object>> traces = new ArrayList<Map<String, Object>>();
                for (Traces trace : eiTrace) {
                    Map<String, Object> traceMap = new HashMap<String, Object>();
                    traceMap.put("date", trace.getAcceptTime().substring(0, 10));
                    traceMap.put("time", trace.getAcceptTime().substring(11, 16));
                    traceMap.put("acceptTime", trace.getAcceptTime());
                    traceMap.put("acceptStation", trace.getAcceptStation());
                    traces.add(traceMap);
                }
                result.put("traces", traces);
            }
        }

        log.info("【请求结束】订单物流跟踪成功！");
        return WxResult.success(result);

    }

    @Transactional(rollbackFor = Exception.class)
    public WxResult<String> combineBalancePay(TpmOrderCombinePrePayReqVo tpmOrderPrePayReqVo, HttpServletRequest request) {
        Integer userId = tpmOrderPrePayReqVo.getUserId();
        if (userId == null) {
            log.error("用户未登录!");
            return WxResult.unlogin();
        }
        if (Objects.isNull(tpmOrderPrePayReqVo.getColdChainOrderId())) {
            log.info("冷链订单id为空");
            return WxResult.error("冷链订单id为空");
        }
        List<Integer> orderIdList = Arrays.asList(tpmOrderPrePayReqVo.getColdChainOrderId(), tpmOrderPrePayReqVo.getCommonOrderId());
        List<TpmOrder> tpmOrderList = orderService.findById(orderIdList);
        if (CollectionUtils.isEmpty(tpmOrderList)) {
            log.info("订单不存在");
            return WxResult.error("订单不存在");
        }
        if (tpmOrderList.stream().anyMatch(order -> !order.getUserId().equals(userId))) {
            return WxResult.badArgumentValue();
        }
        if (tpmOrderList.size() != 2) {
            return WxResult.error("只允许两单合并支付");
        }

        // 检测是否能够支付
        for (TpmOrder order : tpmOrderList) {
            OrderHandleOption handleOption = OrderUtil.build(order);
            if (!handleOption.isPay()) {
                log.error("订单号:{} 付款订单的预支付会话标识失败：{}", order.getOrderSn(), ORDER_PAY_FAIL.desc());
                return WxResult.error(ORDER_PAY_FAIL);
            }
        }
        // 检测是否能够支付
        for (TpmOrder order : tpmOrderList) {
            OrderHandleOption handleOption = OrderUtil.build(order);
            if (!handleOption.isPay()) {
                log.error("订单状态不允许支付");
                return WxResult.error("订单状态不允许支付");
            }
        }

        TpmUser user = userService.findById(userId);
        if (Objects.isNull(user)) {
            return WxResult.error("用户不存在");
        }

        TpmOrder tpmOrder1 = tpmOrderList.get(0);
        TpmOrder tpmOrder2 = tpmOrderList.get(1);

        BigDecimal totalActualPrice = tpmOrder1.getActualPrice().add(tpmOrder2.getActualPrice());
        if (Objects.isNull(user.getBalance()) || user.getBalance().compareTo(totalActualPrice) < 0) {
            return WxResult.error("余额不足");
        }

        user.setBalance(user.getBalance().subtract(totalActualPrice));
        user.setUpdateTime(LocalDateTime.now());
        userService.updateById(user);

        String payId = DateTime.now().toString("yyyyMMdd") +
                StringUtils.leftPad("" + tpmOrder1.getId() + tpmOrder2.getId(), 10, "0");
        for (TpmOrder order : tpmOrderList) {
            order.setPayId(payId);
            order.setPayTime(LocalDateTime.now());
            order.setOrderStatus(OrderUtil.STATUS_PAY);
            order.setPayType(OrderPayTypeEnums.BALANCE.getCode());
            if (orderService.updateWithOptimisticLocker(order) == 0) {
                // 这里可能存在这样一个问题，用户支付和系统自动取消订单发生在同时
                // 如果数据库首先因为系统自动取消订单而更新了订单状态；
                // 此时用户支付完成回调这里也要更新数据库，而由于乐观锁机制这里的更新会失败
                // 因此，这里会重新读取数据库检查状态是否是订单自动取消，如果是则更新成支付状态。
                order = orderService.findById(order.getId());
                int updated = 0;
                if (OrderUtil.isAutoCancelStatus(order)) {
                    order.setPayId(payId);
                    order.setPayTime(LocalDateTime.now());
                    order.setOrderStatus(OrderUtil.STATUS_PAY);
                    order.setPayType(OrderPayTypeEnums.BALANCE.getCode());
                    updated = orderService.updateWithOptimisticLocker(order);
                }
                // 如果updated是0，那么数据库更新失败
                if (updated == 0) {
                    return WxResult.error("更新数据已失效");
                }
            }
        }

        for (TpmOrder tpmOrder : tpmOrderList) {
            TpmBalance tpmBalance = new TpmBalance();
            tpmBalance.setUserId(userId);
            tpmBalance.setMobile(user.getMobile());
            tpmBalance.setOrderSn(tpmOrder.getOrderSn());
            tpmBalance.setAmount(tpmOrder.getActualPrice().multiply(BigDecimal.valueOf(-1)));
            tpmBalance.setLeftBalance(user.getBalance());
            tpmBalance.setOperator(user.getNickname());
            tpmBalance.setStatus(StatusEnum.ENABLED.getKey());
            tpmBalance.setAddTime(LocalDateTime.now());
            tpmBalance.setUpdateTime(LocalDateTime.now());
            tpmBalance.setDeleted(false);
            tpmBalanceMapper.insert(tpmBalance);
            log.info("余额记录插入成功 balance={}", JSONObject.toJSONString(tpmBalance));

        }

        for (TpmOrder tpmOrder : tpmOrderList) {
            //订单支付企业微信提醒
            orderPaidShopWxNotify(tpmOrder);
            printMallOrderKitchen(tpmOrder);
        }
        return WxResult.success("支付成功");
    }


    @Transactional(rollbackFor = Exception.class)
    public WxResult<String> balancePay(TpmOrderPrePayReqVo tpmOrderPrePayReqVo, HttpServletRequest request) {
        Integer userId = tpmOrderPrePayReqVo.getUserId();
        if (userId == null) {
            log.error("用户未登录!");
            return WxResult.unlogin();
        }
        Integer orderId = tpmOrderPrePayReqVo.getOrderId();
        if (orderId == null) {
            return WxResult.badArgument();
        }

        TpmOrder order = orderService.findById(orderId);
        if (order == null) {
            return WxResult.badArgumentValue();
        }
        if (!order.getUserId().equals(userId)) {
            return WxResult.badArgumentValue();
        }

        // 检测是否能够取消
        OrderHandleOption handleOption = OrderUtil.build(order);
        if (!handleOption.isPay()) {
            log.error("订单状态不允许支付");
            return WxResult.error("订单状态不允许支付");
        }

        TpmUser user = userService.findById(userId);
        if (Objects.isNull(user)) {
            return WxResult.error("用户不存在");
        }
        if (Objects.isNull(user.getBalance()) || user.getBalance().compareTo(order.getActualPrice()) < 0) {
            return WxResult.error("余额不足");
        }

        user.setBalance(user.getBalance().subtract(order.getActualPrice()));
        user.setUpdateTime(LocalDateTime.now());
        userService.updateById(user);

        List<TpmOrderGoods> orderGoodsList = orderGoodsService.queryByOid(order.getId());
        String payId = DateTime.now().toString("yyyyMMdd") +
                StringUtils.leftPad("" + order.getId(), 10, "0");

        order.setPayId(payId);
        order.setPayTime(LocalDateTime.now());
        order.setOrderStatus(OrderUtil.STATUS_PAY);
        order.setPayType(OrderPayTypeEnums.BALANCE.getCode());
        if (orderService.updateWithOptimisticLocker(order) == 0) {
            // 这里可能存在这样一个问题，用户支付和系统自动取消订单发生在同时
            // 如果数据库首先因为系统自动取消订单而更新了订单状态；
            // 此时用户支付完成回调这里也要更新数据库，而由于乐观锁机制这里的更新会失败
            // 因此，这里会重新读取数据库检查状态是否是订单自动取消，如果是则更新成支付状态。
            order = orderService.findById(orderId);
            int updated = 0;
            if (OrderUtil.isAutoCancelStatus(order)) {
                order.setPayId(payId);
                order.setPayTime(LocalDateTime.now());
                order.setOrderStatus(OrderUtil.STATUS_PAY);
                order.setPayType(OrderPayTypeEnums.BALANCE.getCode());
                updated = orderService.updateWithOptimisticLocker(order);
            }

            // 如果updated是0，那么数据库更新失败
            if (updated == 0) {
                return WxResult.error("更新数据已失效");
            }
        }
        TpmBalance tpmBalance = new TpmBalance();
        tpmBalance.setUserId(userId);
        tpmBalance.setMobile(user.getMobile());
        tpmBalance.setOrderSn(order.getOrderSn());
        tpmBalance.setAmount(order.getActualPrice().multiply(BigDecimal.valueOf(-1)));
        tpmBalance.setLeftBalance(user.getBalance());
        tpmBalance.setOperator(user.getNickname());
        tpmBalance.setStatus(StatusEnum.ENABLED.getKey());
        tpmBalance.setAddTime(LocalDateTime.now());
        tpmBalance.setUpdateTime(LocalDateTime.now());
        tpmBalance.setDeleted(false);
        tpmBalanceMapper.insert(tpmBalance);
        log.info("余额记录插入成功 balance={}", JSONObject.toJSONString(tpmBalance));
//        notifyService.notifySslMail("新订单通知", OrderUtil.orderHtmlText(order, String.valueOf(order.getId()), orderGoodsList));

        if (Objects.equals(order.getBusinessType(), TpmBusinessTypeEnums.PICKUP_DELIVERY.getCode())) {
            //订单支付企业微信提醒
            List<TpmOrderGoods> orderGoods = orderGoodsService.queryByOid(order.getId());
            TpmCouponCodeUseLog tpmCouponCodeUseLog = tpmCouponCodeUseLogService.findByOrderId(order.getId());
            orderPaidWxNotify(order);
            pushOrder2JuDanKe(order, orderGoods);
            callJuDanKePrint(order, orderGoods, tpmCouponCodeUseLog);
        } else if (Objects.equals(order.getBusinessType(), TpmBusinessTypeEnums.EXPRESS.getCode())) {
            //订单支付企业微信提醒
            orderPaidShopWxNotify(order);
            printMallOrderKitchen(order);
        }
        return WxResult.success("支付成功");
    }

    public WxResult<TpmUnTakeMealResVo> existUnTakeMeal(TpmUnTakeMealReqVo reqVo) {
        TpmUnTakeMealResVo tpmUnTakeMealResVo = new TpmUnTakeMealResVo();
        tpmUnTakeMealResVo.setHasUnTakeMeal(false);
        Integer userId = reqVo.getUserId();
        if (Objects.isNull(userId)) {
            return WxResult.success(tpmUnTakeMealResVo);
        }
        List<TpmOrder> tpmOrderList = orderService.queryUserExistUnTakeMeal(userId);
        if (CollectionUtils.isEmpty(tpmOrderList)) {
            return WxResult.success(tpmUnTakeMealResVo);
        }
        tpmUnTakeMealResVo.setHasUnTakeMeal(true);
        if (tpmOrderList.size() > 1) {
            tpmUnTakeMealResVo.setMultiUnTakeMeal(true);
            return WxResult.success(tpmUnTakeMealResVo);
        }
        TpmOrder tpmOrder = tpmOrderList.get(0);
        tpmUnTakeMealResVo.setMealCode(tpmOrder.getMealCode());
        tpmUnTakeMealResVo.setMealQrCode(tpmOrder.getMealQrCode());
        tpmUnTakeMealResVo.setOrderId(tpmOrder.getId());
        return WxResult.success(tpmUnTakeMealResVo);
    }

    public Boolean isFinishTakeMeal(Integer orderId) {
        TpmOrder tpmOrder = orderService.findById(orderId);
        return this.isFinishTakeMeal(tpmOrder);
    }

    public Boolean isFinishTakeMeal(TpmOrder tpmOrder) {
        if (Objects.isNull(tpmOrder)) {
            return true;
        }
        Short orderStatus = tpmOrder.getOrderStatus();
        if (Objects.equals(orderStatus, OrderUtil.STATUS_CREATE) ||
                Objects.equals(orderStatus, OrderUtil.STATUS_SHIP) ||
                Objects.equals(orderStatus, OrderUtil.STATUS_PREPARE) ||
                Objects.equals(orderStatus, OrderUtil.STATUS_CONFIRM) ||
                Objects.equals(orderStatus, OrderUtil.STATUS_CANCEL) ||
                Objects.equals(orderStatus, OrderUtil.STATUS_AUTO_CANCEL) ||
                Objects.equals(orderStatus, OrderUtil.STATUS_REFUND) ||
                Objects.equals(orderStatus, OrderUtil.STATUS_REFUND_CONFIRM) ||
                Objects.equals(orderStatus, OrderUtil.STATUS_AUTO_CONFIRM)
        ) {
            return true;
        }
        if (Objects.equals(tpmOrder.getFreightType(), (byte) 1) || Objects.equals(tpmOrder.getFreightType(), (byte) 2)) {
            if (Objects.isNull(tpmOrder.getMealPickupStatus())) {
                return false;
            }
            if (Objects.isNull(tpmOrder.getMealPickupTime())) {
                return false;

            }
            return tpmOrder.getMealPickupStatus();
        }
        return true;
    }
}
