package com.pioneer.mall.wx.service;

import com.alibaba.fastjson.JSONObject;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.bean.result.BaseWxPayResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.pioneer.mall.core.consts.CommConsts;
import com.pioneer.mall.core.system.SystemConfig;
import com.pioneer.mall.core.util.WechatNotifyUtils;
import com.pioneer.mall.core.util.WxResult;
import com.pioneer.mall.core.util.WxUploadShippingInfoUtils;
import com.pioneer.mall.db.bean.request.TpmRechargeRequest;
import com.pioneer.mall.db.dao.TpmRechargeMapper;
import com.pioneer.mall.db.domain.*;
import com.pioneer.mall.db.dto.TpmPointDto;
import com.pioneer.mall.db.service.*;
import com.pioneer.mall.db.util.OrderUtil;
import com.pioneer.mall.wx.util.IpUtil;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.io.IOUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

import static com.pioneer.mall.db.util.WxResponseCode.AUTH_OPENID_UNACCESS;
import static com.pioneer.mall.db.util.WxResponseCode.ORDER_PAY_FAIL;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/10 21:00
 */
@Service
public class WxRechargeService {
    private static final Logger logger = LoggerFactory.getLogger(WxRechargeService.class);

    @Autowired
    private TpmRechargeService tpmRechargeService;
    @Autowired
    private TpmRechargeMapper tpmRechargeMapper;
    @Autowired
    private TpmRechargeConfigService tpmRechargeConfigService;
    @Autowired
    private TpmUserService tpmUserService;
    @Autowired
    private WxPayService wxPayService;
    @Autowired
    private TpmBalanceService tpmBalanceService;
    @Autowired
    private TpmPointService tpmPointService;

    @Autowired
    private TpmUserFormIdService formIdService;
    @Autowired
    private WxUploadShippingInfoUtils wxUploadShippingInfoUtils;


    /**
     * 充值
     *
     * @param tpmRechargeRequest
     * @param request
     * @return
     */
    public WxResult<WxPayMpOrderResult> preRecharge(TpmRechargeRequest tpmRechargeRequest, HttpServletRequest request) {
        if (Objects.isNull(tpmRechargeRequest)) {
            return WxResult.badArgumentValue();
        }
        Integer userId = tpmRechargeRequest.getUserId();
        if (userId == null) {
            logger.error("充值订单的预支付会话标识失败：用户未登录!");
            return WxResult.unlogin();
        }
        Integer configId = tpmRechargeRequest.getConfigId();
        TpmRechargeConfig tpmRechargeConfig = tpmRechargeConfigService.findById(configId);
        if (Objects.isNull(tpmRechargeConfig)) {
            logger.error("充值金额选项已关闭");
            return WxResult.error(-1, "充值金额选项已关闭");
        }
//        if (!Objects.equals(tpmRechargeConfig.getAmount(), tpmRechargeRequest.getRechargeAmount())) {
//            logger.error("充值金额选项已发生变化，请重新发起充值");
//            return WxResult.error(-1, "充值金额选项已发生变化，请重新发起充值");
//        }
        TpmUser user = tpmUserService.findById(userId);
        String openid = user.getWeixinOpenid();
        if (openid == null) {
            logger.error("充值订单的预支付会话标识失败：{}", AUTH_OPENID_UNACCESS.desc());
            return WxResult.error(AUTH_OPENID_UNACCESS);
        }
        String tradeNo = "CZ" + DateTime.now().toString("yyMMddHHmmSS") + "_" + userId;
        tpmRechargeService.createNewRecharge(user, tpmRechargeRequest, tradeNo);
        WxPayMpOrderResult result = null;
        try {
            WxPayUnifiedOrderRequest orderRequest = new WxPayUnifiedOrderRequest();
            orderRequest.setOutTradeNo(tradeNo);
            orderRequest.setOpenid(openid);
            orderRequest.setBody(CommConsts.DEFAULT_ORDER_FIX + tradeNo);
            // 元转成分
            BigDecimal actualPrice = tpmRechargeRequest.getRechargeAmount();
            int fee = actualPrice.multiply(new BigDecimal(100)).intValue();
            orderRequest.setTotalFee(fee);
            orderRequest.setSpbillCreateIp(IpUtil.getIpAddr(request));
            orderRequest.setNotifyUrl("https://www.7riverlight.com/wx/recharge/rechargeNotify");
            result = wxPayService.createOrder(orderRequest);

            // 缓存prepayID用于后续模版通知
            String prepayId = result.getPackageValue();
            prepayId = prepayId.replace("prepay_id=", "");
            TpmUserFormid userFormid = new TpmUserFormid();
            userFormid.setOpenid(user.getWeixinOpenid());
            userFormid.setFormid(prepayId);
            userFormid.setIsprepay(true);
            userFormid.setUseamount(3);
            userFormid.setExpireTime(LocalDateTime.now().plusDays(7));
            formIdService.addUserFormid(userFormid);

        } catch (Exception e) {
            logger.error("支付订单的预支付会话标识失败：{}", ORDER_PAY_FAIL.desc());
            e.printStackTrace();
            return WxResult.error(ORDER_PAY_FAIL);
        }
        return WxResult.success(result);
    }

    public String rechargePayNotify(HttpServletRequest request, HttpServletResponse response) {
        String xmlResult = null;
        try {
            xmlResult = IOUtils.toString(request.getInputStream(), request.getCharacterEncoding());
        } catch (IOException e) {
            logger.error("微信付款成功或失败回调失败：{}", "获取回调消息内容错误!");
            e.printStackTrace();
            return WxPayNotifyResponse.fail(e.getMessage());
        }

        WxPayOrderNotifyResult result = null;
        try {
            result = wxPayService.parseOrderNotifyResult(xmlResult);
        } catch (WxPayException e) {
            logger.error("微信付款成功或失败回调失败：{}", "格式化消息内容错误!");
            e.printStackTrace();
            return WxPayNotifyResponse.fail(e.getMessage());
        }

        logger.info("处理腾讯支付平台的订单支付：{}", JSONObject.toJSONString(result));

        String orderSn = result.getOutTradeNo();
        String payId = result.getTransactionId();

        // 分转化成元
        String totalFee = BaseWxPayResult.fenToYuan(result.getTotalFee());
        TpmRecharge tpmRecharge = tpmRechargeService.findBySn(orderSn);
        if (tpmRecharge == null) {
            logger.error("微信付款成功或失败回调失败：{}", "充值订单不存在 sn=" + orderSn);
            return WxPayNotifyResponse.fail("充值订单不存在 sn=" + orderSn);
        }

        // 检查这个订单是否已经处理过
        if (OrderUtil.isPayStatus(tpmRecharge) && tpmRecharge.getPayId() != null) {
            logger.warn("警告：微信付款成功或失败回调：{}", "订单已经处理成功!");
            return WxPayNotifyResponse.success("订单已经处理成功!");
        }

        // 检查支付订单金额
        if (!totalFee.equals(tpmRecharge.getRechargeAmount().toString())) {
            logger.error("微信付款成功或失败回调失败：{}", tpmRecharge.getOrderNo() + " : 支付金额不符合 totalFee=" + totalFee);
            return WxPayNotifyResponse.fail(tpmRecharge.getOrderNo() + " : 支付金额不符合 totalFee=" + totalFee);
        }
        // 邮件通知的订单id
//        String orderIds =  tpmRecharge.getId().intValue();
        tpmRecharge.setPayId(payId);
        tpmRecharge.setPayTime(LocalDateTime.now());
        tpmRecharge.setStatus(OrderUtil.STATUS_PAY.intValue());
//            if (orderService.updateWithOptimisticLocker(order) == 0) {
//                // 这里可能存在这样一个问题，用户支付和系统自动取消订单发生在同时
//                // 如果数据库首先因为系统自动取消订单而更新了订单状态；
//                // 此时用户支付完成回调这里也要更新数据库，而由于乐观锁机制这里的更新会失败
//                // 因此，这里会重新读取数据库检查状态是否是订单自动取消，如果是则更新成支付状态。
//                order = orderService.findBySn(orderSn);
//                int updated = 0;
//                if (OrderUtil.isAutoCancelStatus(order)) {
//                    order.setPayId(payId);
//                    order.setPayTime(LocalDateTime.now());
//                    order.setOrderStatus(OrderUtil.STATUS_PAY);
//                    updated = orderService.updateWithOptimisticLocker(order);
//                }
//
//                // 如果updated是0，那么数据库更新失败
//                if (updated == 0) {
//                    return WxPayNotifyResponse.fail("更新数据已失效");
//                }
//            }
//        }
        tpmRechargeMapper.updateByPrimaryKeySelective(tpmRecharge);
        // TODO 发送邮件和短信通知，这里采用异步发送
        // 订单支付成功以后，会发送短信给用户，以及发送邮件给管理员
        // notifyService.notifyMail("新订单通知", order.toString());
//        notifyService.notifySslMail("新订单通知", OrderUtil.orderHtmlText(order, orderIds, orderGoodsList));
        // 这里微信的短信平台对参数长度有限制，所以将订单号只截取后6位，暂时屏蔽，因为已有微信模板提醒
        // notifyService.notifySmsTemplateSync(order.getMobile(),
        // NotifyType.PAY_SUCCEED, new String[]{orderSn.substring(8, 14)});

        // 请依据自己的模版消息配置更改参数
//        String[] parms = new String[] { tpmRecharge.getOrderNo(), order.getOrderPrice().toString(),
//                DateTimeUtil.getDateTimeDisplayString(order.getAddTime()), order.getConsignee(), order.getMobile(),
//                order.getAddress() };

        // notifyService.notifyWxTemplate(result.getOpenid(), NotifyType.PAY_SUCCEED,
        // parms, "pages/index/index?orderId=" + order.getId());
//        notifyService.notifyWxTemplate(result.getOpenid(), NotifyType.PAY_SUCCEED, parms, "/pages/ucenter/order/order");
        Integer userId = tpmRecharge.getUserId();
        TpmUser tpmUser = tpmUserService.findById(userId);
        if (Objects.isNull(tpmUser)) {
            logger.error("用户id:{} 不存在 ", userId);
            return WxPayNotifyResponse.fail("用户不存在");
        }
        TpmBalance tpmBalance = new TpmBalance();
        BigDecimal totalAmount = tpmRecharge.getRechargeAmount();
        if (Objects.nonNull(tpmRecharge.getGiveAmount())) {
            totalAmount = totalAmount.add(tpmRecharge.getGiveAmount());
        }
        tpmBalance.setAmount(totalAmount);
        tpmBalance.setUserId(tpmUser.getId());
        tpmBalance.setOrderSn(tpmRecharge.getOrderNo());
//                tpmBalance.setMerchantId(accountInfo.getMerchantId());
        tpmBalance.setRemark("充值余额成功,新增余额:"+tpmBalance.getAmount());
        try {
            tpmBalanceService.addBalance(tpmBalance, true);
        } catch (Exception e) {
            logger.error("用户id:{} 用户增加余额失败", userId);
            return WxPayNotifyResponse.fail("用户增加余额失败");
        }
        if (Objects.nonNull(tpmRecharge.getGivePoint())) {
            try {
                TpmPointDto tpmPointDto = new TpmPointDto();
                tpmPointDto.setUserId(userId);
                tpmPointDto.setOrderSn(tpmRecharge.getOrderNo());
                tpmPointDto.setAmount(tpmRecharge.getGivePoint());
                tpmPointDto.setDescription("订单:" + tpmRecharge.getOrderNo() + "充值赠送积分:" + tpmRecharge.getGivePoint());
                tpmPointService.addPoint(tpmPointDto);
            } catch (Exception e) {
                logger.error("用户id:{} 用户增加积分失败", userId,e);
                return WxPayNotifyResponse.fail("用户增加积分失败");
            }
        }
        try {
            wxUploadShippingInfoUtils.doUploadShippingInfoVirtual(tpmRecharge, tpmUser);
        }catch (Exception e){
            String errorMsg = "用户id:"+userId+" 充值单号:"+orderSn+" 上传虚拟发货信息失败";
            logger.error(errorMsg,e);
            WechatNotifyUtils.wechatNotifyMd(SystemConfig.getWechatNotifyUrl(), null, errorMsg);
        }
        logger.info("【请求结束】微信付款成功或失败回调:响应结果:{}", "处理成功!");
        return WxPayNotifyResponse.success("处理成功!");
    }

}
