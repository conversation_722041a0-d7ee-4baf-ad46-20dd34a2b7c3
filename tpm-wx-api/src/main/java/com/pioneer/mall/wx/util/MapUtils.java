package com.pioneer.mall.wx.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pioneer.mall.core.system.SystemConfig;
import com.pioneer.mall.core.util.WechatNotifyUtils;
import com.pioneer.mall.db.dto.TpmShopInfoDTO;
import com.pioneer.mall.db.service.TpmFreightStepPriceService;
import com.pioneer.mall.wx.bean.res.DistanceMatrixResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/5/29 17:22
 */
@Component
public class MapUtils {
    private static final Logger logger = LoggerFactory.getLogger(TpmFreightStepPriceService.class);

    public static void main(String[] args) throws Exception {
        //获取接口耗时时间
        Long start = System.currentTimeMillis();
//        getEBicyclingDistance();
        Long end = System.currentTimeMillis();
        System.out.println(end - start);
    }

    public static int getEBicyclingDistance(String from, String to) throws Exception {
        Map<String, Object> param = new HashMap<>();
//        param.put("from", "30.30930519104004,120.2583236694336");
//        param.put("to", "30.212976,120.214897");
        param.put("from", from);
        param.put("to", to);
        param.put("key", "EB7BZ-W7X6J-KL7FS-X7M53-K63S5-WTFLA");
        HttpRequest httpRequest = HttpRequest.get("https://apis.map.qq.com/ws/direction/v1/ebicycling/");
        httpRequest.form(param);
        HttpResponse execute = httpRequest.execute();
        if (execute.isOk()) {
            String body = execute.body();
            System.out.println(body);
            JSONObject jsonObject = JSON.parseObject(body);
            Integer status = jsonObject.getInteger("status");
            if (status == 0) {
                // 获取distance值
                JSONObject result = jsonObject.getJSONObject("result");
                JSONArray routes = result.getJSONArray("routes");
                if (!routes.isEmpty()) {
                    JSONObject route = routes.getJSONObject(0);
                    int distance = route.getInteger("distance");
                    logger.info("根据经纬度:" + to + "计算得出 Distance: " + distance);
                    return distance;
                } else {
                    logger.error("根据经纬度:" + to + "没有找到任何路线");
                    throw new Exception("没有找到任何路线");
                }
            } else {
                String message = jsonObject.getString("message");
                String errorMsg = "距离计算接口调用失败，状态码:" + status + " 错误信息: " + message;
                logger.error(errorMsg);
                WechatNotifyUtils.wechatNoteSend(SystemConfig.getWechatNotifyUrl(), null, errorMsg);
                throw new Exception(errorMsg);
            }
        }
        return -1;
    }
    public static List<TpmShopInfoDTO> calculateDistance(String from, List<TpmShopInfoDTO> tpmShopInfoDTOS) throws Exception {
        // 参数校验
        if (from == null || from.isEmpty() || tpmShopInfoDTOS == null || tpmShopInfoDTOS.isEmpty()) {
            throw new IllegalArgumentException("起点经纬度和店铺列表不能为空");
        }

        try {
            // 构建请求参数映射
            Map<String, Integer> locationToShopIdMap = buildLocationToShopIdMap(tpmShopInfoDTOS);
            Map<Integer, TpmShopInfoDTO> shopIdToDtoMap = buildShopIdToDtoMap(tpmShopInfoDTOS);

            // 执行API请求并解析结果
            DistanceMatrixResponse response = executeDistanceApi(from, locationToShopIdMap);

            // 处理结果并设置距离信息
            processDistanceResults(response, shopIdToDtoMap, locationToShopIdMap);

            // 找出最近的店铺并排序
            List<TpmShopInfoDTO> sortedShops = sortAndMarkNearestShop(shopIdToDtoMap);

            logger.info("成功计算{}个店铺的距离信息", sortedShops.size());
            return sortedShops;
        } catch (Exception e) {
            logger.error("计算店铺距离失败", e);
            throw new RuntimeException("计算店铺距离失败: " + e.getMessage(), e);
        }
    }

    private static Map<String, Integer> buildLocationToShopIdMap(List<TpmShopInfoDTO> shopList) {
        return shopList.stream()
                .collect(Collectors.toMap(
                        shop -> shop.getLatitude() + "," + shop.getLongitude(),
                        TpmShopInfoDTO::getId,
                        (existing, replacement) -> existing // 处理重复经纬度的策略
                ));
    }

    private static Map<Integer, TpmShopInfoDTO> buildShopIdToDtoMap(List<TpmShopInfoDTO> shopList) {
        return shopList.stream()
                .collect(Collectors.toMap(
                        TpmShopInfoDTO::getId,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
    }

    private static DistanceMatrixResponse executeDistanceApi(String from, Map<String, Integer> locationToShopIdMap) throws IOException {
        // 构建请求参数
        Map<String, Object> param = new HashMap<>();
        param.put("from", from);
        param.put("to", String.join(";", locationToShopIdMap.keySet()));
        param.put("mode", "walking");
        param.put("key", "EB7BZ-W7X6J-KL7FS-X7M53-K63S5-WTFLA");

        logger.info("calculateDistance param={}", JSON.toJSONString(param));

        // 执行API请求
        HttpRequest httpRequest = HttpRequest.get("https://apis.map.qq.com/ws/distance/v1/matrix");
        httpRequest.form(param);
        HttpResponse response = httpRequest.execute();

        if (!response.isOk()) {
            throw new IOException("API请求失败，状态码: " + response.getStatus());
        }

        // 解析响应
        String body = response.body();
        ObjectMapper mapper = new ObjectMapper();
        DistanceMatrixResponse apiResponse = mapper.readValue(body, DistanceMatrixResponse.class);

        // 检查API状态
        if (apiResponse.getStatus() != 0) {
            throw new IOException("API调用失败：" + apiResponse.getMessage());
        }

        // 验证响应结构
        List<DistanceMatrixResponse.Row> rows = apiResponse.getResult().getRows();
        if (rows == null || rows.isEmpty() || rows.get(0).getElements() == null) {
            throw new IOException("无效的API响应格式：缺少rows或elements数据");
        }

        return apiResponse;
    }

    private static void processDistanceResults(DistanceMatrixResponse response,
                                               Map<Integer, TpmShopInfoDTO> shopIdToDtoMap,
                                               Map<String, Integer> locationToShopIdMap) {
        List<DistanceMatrixResponse.Element> elements = response.getResult().getRows().get(0).getElements();

        // 确保API返回的元素数量与请求的店铺数量一致
        if (elements.size() != locationToShopIdMap.size()) {
            logger.warn("API返回的距离元素数量({})与请求的店铺数量({})不一致",
                    elements.size(), locationToShopIdMap.size());
        }

        int index = 0;
        for (String location : locationToShopIdMap.keySet()) {
            if (index >= elements.size()) break;

            Integer shopId = locationToShopIdMap.get(location);
            TpmShopInfoDTO shop = shopIdToDtoMap.get(shopId);

            if (shop != null) {
                DistanceMatrixResponse.Element element = elements.get(index);
                shop.setDistance(element.getDistance());
            }

            index++;
        }
    }

    private static List<TpmShopInfoDTO> sortAndMarkNearestShop(Map<Integer, TpmShopInfoDTO> shopIdToDtoMap) {
        List<TpmShopInfoDTO> sortedShops = new ArrayList<>(shopIdToDtoMap.values());

        // 按距离排序
        sortedShops.sort(Comparator.comparingInt(TpmShopInfoDTO::getDistance));

        // 标记最近的店铺
        if (!sortedShops.isEmpty()) {
            sortedShops.get(0).setNearest(true);
        }

        return sortedShops;
    }


}
