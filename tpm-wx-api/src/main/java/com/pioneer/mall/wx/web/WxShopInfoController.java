package com.pioneer.mall.wx.web;

import com.pioneer.mall.core.util.ConvertUtil;
import com.pioneer.mall.core.util.WxResult;
import com.pioneer.mall.db.domain.TpmShopInfo;
import com.pioneer.mall.db.dto.TpmShopInfoDTO;
import com.pioneer.mall.db.dto.TpmShopOpenStatusDTO;
import com.pioneer.mall.db.service.TpmShopHoursService;
import com.pioneer.mall.db.service.TpmShopInfoService;
import com.pioneer.mall.wx.util.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/9/5 23:33
 */
@Slf4j
@RestController
@RequestMapping("/wx/shop")
public class WxShopInfoController {
    @Autowired
    private TpmShopHoursService tpmShopHoursService;
    @Autowired
    private TpmShopInfoService tpmShopInfoService;

    @GetMapping("/getOpenStatus")
    public WxResult<TpmShopOpenStatusDTO> getOpenStatus(@RequestParam(name = "shopId") Integer shopId) {
        log.info("【请求开始】获取店铺营业时间,请求参数,shopId:{}", shopId);
        try {
            TpmShopOpenStatusDTO shopOpenStatus = tpmShopHoursService.getShopOpenStatus(shopId);
            if (Objects.isNull(shopOpenStatus)){
                shopOpenStatus = new TpmShopOpenStatusDTO();
                shopOpenStatus.setShopId(shopId);
                shopOpenStatus.setOpen(false);
                shopOpenStatus.setStatusDesc("店铺未营业");
                return WxResult.success(shopOpenStatus);
            }
            if (!shopOpenStatus.getOpen()&&Objects.isNull(shopOpenStatus.getStatusDesc())){
                shopOpenStatus.setStatusDesc("店铺未营业");
            }
            return WxResult.success(shopOpenStatus);
        }catch (Exception e){
            return WxResult.error("获取店铺营业时间失败");
        }
    }

    /**
     * 获取全部门店列表
     * @return
     */
    @GetMapping("/getShopList")
    public WxResult<List<TpmShopInfoDTO>> getShopList() {
        log.info("【请求开始】获取店铺列表");
        List<TpmShopInfo> tpmShopInfoList = tpmShopInfoService.getList();
        List<TpmShopInfoDTO> tpmShopInfoDTOS = ConvertUtil.listConvert(tpmShopInfoList, TpmShopInfoDTO.class);
        return WxResult.success(tpmShopInfoDTOS);
    }

    /**
     * 获取全部门店列表
     * @return
     */
    @GetMapping("/getShopListByLatAndLng")
    public WxResult<List<TpmShopInfoDTO>> getShopList(String latAndLng) throws Exception {
        log.info("【请求开始】获取店铺列表 latAndLng={}",latAndLng);
        List<TpmShopInfo> tpmShopInfoList = tpmShopInfoService.getList();
        List<TpmShopInfoDTO> tpmShopInfoDTOS = ConvertUtil.listConvert(tpmShopInfoList, TpmShopInfoDTO.class);
        List<TpmShopInfoDTO> result = MapUtils.calculateDistance(latAndLng, tpmShopInfoDTOS);
        log.info("【请求结束】获取店铺列表,返回结果:{}", result);
        return WxResult.success(result);
    }

    @GetMapping("/getShopLocation")
    public WxResult<TpmShopInfoDTO> getShopLocation(@RequestParam(name = "shopId") Integer shopId) {
        log.info("【请求开始】获取店铺地址,请求参数,shopId:{}", shopId);
        TpmShopInfo tpmShopInfo = tpmShopInfoService.getShopInfoById(shopId);
        TpmShopInfoDTO tpmShopInfoDTO = new TpmShopInfoDTO();
        tpmShopInfoDTO.setShopName(tpmShopInfo.getShopName());
        tpmShopInfoDTO.setLongitude(tpmShopInfo.getLongitude());
        tpmShopInfoDTO.setLatitude(tpmShopInfo.getLatitude());
        tpmShopInfoDTO.setAddress(tpmShopInfo.getAddress());
        tpmShopInfoDTO.setId(tpmShopInfo.getId());
        tpmShopInfoDTO.setTelephone(tpmShopInfo.getPhoneNumber());
        return WxResult.success(tpmShopInfoDTO);
    }
}
