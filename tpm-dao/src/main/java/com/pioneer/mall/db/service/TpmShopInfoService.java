package com.pioneer.mall.db.service;

import com.pioneer.mall.db.dao.TpmShopHoursMapper;
import com.pioneer.mall.db.dao.TpmShopInfoMapper;
import com.pioneer.mall.db.domain.TpmShopInfo;
import com.pioneer.mall.db.domain.TpmShopInfoExample;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/9/24 21:21
 */
@Service
public class TpmShopInfoService {

    @Resource
    private TpmShopInfoMapper tpmShopInfoMapper;

    public List<TpmShopInfo> getList(){
        TpmShopInfoExample example = new TpmShopInfoExample();
        example.createCriteria().andDeletedEqualTo(false);
        return tpmShopInfoMapper.selectByExample(example);
    }

    public TpmShopInfo getShopInfoById(Integer shopId) {
        if (shopId == null) {
            return null;
        }
        return tpmShopInfoMapper.selectByPrimaryKey(shopId);
    }

    public String getLatitudeByDefaultShopInfo() {
        TpmShopInfo tpmShopInfo = getShopInfoById(1);
        if (tpmShopInfo != null) {
            return tpmShopInfo.getLatitude().toString();
        }
        return null;
    }

    public String getLongitudeByDefaultShopInfo() {
        TpmShopInfo tpmShopInfo = getShopInfoById(1);
        if (tpmShopInfo != null) {
            return tpmShopInfo.getLongitude().toString();
        }
        return null;
    }

    /**
     * 获取默认店铺是否支持自提
     *
     * @return 是否开启自提（0 表示否，1 表示是）
     */
    public Boolean getSelfPickupByDefaultShopInfo() {
        TpmShopInfo tpmShopInfo = getShopInfoById(1);
        if (tpmShopInfo != null) {
            return tpmShopInfo.getSelfPickup();
        }
        return null;
    }


    /**
     * 获取默认店铺是否支持堂食
     *
     * @return 是否开启堂食（0 表示否，1 表示是）
     */
    public Boolean getDineInByDefaultShopInfo() {
        TpmShopInfo tpmShopInfo = getShopInfoById(1);
        if (tpmShopInfo != null) {
            return tpmShopInfo.getDineIn();
        }
        return null;
    }

    /**
     * 获取默认店铺是否支持配送
     *
     * @return 是否开启外送（0 表示否，1 表示是）
     */
    public Boolean getDeliveryByDefaultShopInfo() {
        TpmShopInfo tpmShopInfo = getShopInfoById(1);
        if (tpmShopInfo != null) {
            return tpmShopInfo.getDelivery();
        }
        return null;
    }

}
