<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.generate.dao.TpmGoodsMapper">
  <resultMap id="BaseResultMap" type="com.pioneer.mall.generate.domain.TpmGoods">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="goods_sn" jdbcType="VARCHAR" property="goodsSn" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="category_id" jdbcType="INTEGER" property="categoryId" />
    <result column="shop_id" jdbcType="INTEGER" property="shopId" />
    <result column="brand_id" jdbcType="INTEGER" property="brandId" />
    <result column="gallery" jdbcType="VARCHAR" property="gallery" />
    <result column="keywords" jdbcType="VARCHAR" property="keywords" />
    <result column="brief" jdbcType="VARCHAR" property="brief" />
    <result column="is_on_sale" jdbcType="BIT" property="isOnSale" />
    <result column="is_sell_out" jdbcType="BIT" property="isSellOut" />
    <result column="sort_order" jdbcType="SMALLINT" property="sortOrder" />
    <result column="pic_url" jdbcType="VARCHAR" property="picUrl" />
    <result column="share_url" jdbcType="VARCHAR" property="shareUrl" />
    <result column="is_new" jdbcType="BIT" property="isNew" />
    <result column="is_hot" jdbcType="BIT" property="isHot" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="counter_price" jdbcType="DECIMAL" property="counterPrice" />
    <result column="retail_price" jdbcType="DECIMAL" property="retailPrice" />
    <result column="inventory_num" jdbcType="INTEGER" property="inventoryNum" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="browse" jdbcType="INTEGER" property="browse" />
    <result column="sales" jdbcType="INTEGER" property="sales" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="commpany" jdbcType="VARCHAR" property="commpany" />
    <result column="wholesale_price" jdbcType="DECIMAL" property="wholesalePrice" />
    <result column="approve_status" jdbcType="TINYINT" property="approveStatus" />
    <result column="approve_msg" jdbcType="VARCHAR" property="approveMsg" />
    <result column="brokerage_type" jdbcType="TINYINT" property="brokerageType" />
    <result column="brokerage_price" jdbcType="DECIMAL" property="brokeragePrice" />
    <result column="sale_range" jdbcType="INTEGER" property="saleRange" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="transport_type" jdbcType="INTEGER" property="transportType" />
    <result column="weigth" jdbcType="DECIMAL" property="weigth" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.pioneer.mall.generate.domain.TpmGoods">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="detail" jdbcType="LONGVARCHAR" property="detail" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, goods_sn, `name`, category_id, shop_id, brand_id, gallery, keywords, brief, is_on_sale, 
    is_sell_out, sort_order, pic_url, share_url, is_new, is_hot, unit, counter_price, 
    retail_price, inventory_num, add_time, update_time, `browse`, sales, deleted, commpany, 
    wholesale_price, approve_status, approve_msg, brokerage_type, brokerage_price, sale_range, 
    business_type, transport_type, weigth
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    detail
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.pioneer.mall.generate.domain.TpmGoodsExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tpm_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.pioneer.mall.generate.domain.TpmGoodsExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tpm_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <if test="example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, goods_sn, `name`, category_id, shop_id, brand_id, gallery, keywords, brief, is_on_sale, 
          is_sell_out, sort_order, pic_url, share_url, is_new, is_hot, unit, counter_price, 
          retail_price, inventory_num, add_time, update_time, `browse`, sales, deleted, commpany, 
          wholesale_price, approve_status, approve_msg, brokerage_type, brokerage_price, 
          sale_range, business_type, transport_type, weigth, detail
      </otherwise>
    </choose>
    from tpm_goods
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tpm_goods
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tpm_goods
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        1
      </when>
      <otherwise>
        0
      </otherwise>
    </choose>
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, goods_sn, `name`, category_id, shop_id, brand_id, gallery, keywords, brief, is_on_sale, 
          is_sell_out, sort_order, pic_url, share_url, is_new, is_hot, unit, counter_price, 
          retail_price, inventory_num, add_time, update_time, `browse`, sales, deleted, commpany, 
          wholesale_price, approve_status, approve_msg, brokerage_type, brokerage_price, 
          sale_range, business_type, transport_type, weigth, detail
      </otherwise>
    </choose>
    from tpm_goods
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_goods
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.pioneer.mall.generate.domain.TpmGoodsExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.pioneer.mall.generate.domain.TpmGoods">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_goods (goods_sn, `name`, category_id, 
      shop_id, brand_id, gallery, 
      keywords, brief, is_on_sale, 
      is_sell_out, sort_order, pic_url, 
      share_url, is_new, is_hot, unit, 
      counter_price, retail_price, inventory_num, 
      add_time, update_time, `browse`, 
      sales, deleted, commpany, 
      wholesale_price, approve_status, approve_msg, 
      brokerage_type, brokerage_price, sale_range, 
      business_type, transport_type, weigth, 
      detail)
    values (#{goodsSn,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{categoryId,jdbcType=INTEGER}, 
      #{shopId,jdbcType=INTEGER}, #{brandId,jdbcType=INTEGER}, #{gallery,jdbcType=VARCHAR}, 
      #{keywords,jdbcType=VARCHAR}, #{brief,jdbcType=VARCHAR}, #{isOnSale,jdbcType=BIT}, 
      #{isSellOut,jdbcType=BIT}, #{sortOrder,jdbcType=SMALLINT}, #{picUrl,jdbcType=VARCHAR}, 
      #{shareUrl,jdbcType=VARCHAR}, #{isNew,jdbcType=BIT}, #{isHot,jdbcType=BIT}, #{unit,jdbcType=VARCHAR}, 
      #{counterPrice,jdbcType=DECIMAL}, #{retailPrice,jdbcType=DECIMAL}, #{inventoryNum,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{browse,jdbcType=INTEGER}, 
      #{sales,jdbcType=INTEGER}, #{deleted,jdbcType=BIT}, #{commpany,jdbcType=VARCHAR}, 
      #{wholesalePrice,jdbcType=DECIMAL}, #{approveStatus,jdbcType=TINYINT}, #{approveMsg,jdbcType=VARCHAR}, 
      #{brokerageType,jdbcType=TINYINT}, #{brokeragePrice,jdbcType=DECIMAL}, #{saleRange,jdbcType=INTEGER}, 
      #{businessType,jdbcType=INTEGER}, #{transportType,jdbcType=INTEGER}, #{weigth,jdbcType=DECIMAL}, 
      #{detail,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.pioneer.mall.generate.domain.TpmGoods">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_goods
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="goodsSn != null">
        goods_sn,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="brandId != null">
        brand_id,
      </if>
      <if test="gallery != null">
        gallery,
      </if>
      <if test="keywords != null">
        keywords,
      </if>
      <if test="brief != null">
        brief,
      </if>
      <if test="isOnSale != null">
        is_on_sale,
      </if>
      <if test="isSellOut != null">
        is_sell_out,
      </if>
      <if test="sortOrder != null">
        sort_order,
      </if>
      <if test="picUrl != null">
        pic_url,
      </if>
      <if test="shareUrl != null">
        share_url,
      </if>
      <if test="isNew != null">
        is_new,
      </if>
      <if test="isHot != null">
        is_hot,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="counterPrice != null">
        counter_price,
      </if>
      <if test="retailPrice != null">
        retail_price,
      </if>
      <if test="inventoryNum != null">
        inventory_num,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="browse != null">
        `browse`,
      </if>
      <if test="sales != null">
        sales,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="commpany != null">
        commpany,
      </if>
      <if test="wholesalePrice != null">
        wholesale_price,
      </if>
      <if test="approveStatus != null">
        approve_status,
      </if>
      <if test="approveMsg != null">
        approve_msg,
      </if>
      <if test="brokerageType != null">
        brokerage_type,
      </if>
      <if test="brokeragePrice != null">
        brokerage_price,
      </if>
      <if test="saleRange != null">
        sale_range,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="transportType != null">
        transport_type,
      </if>
      <if test="weigth != null">
        weigth,
      </if>
      <if test="detail != null">
        detail,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="goodsSn != null">
        #{goodsSn,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=INTEGER},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=INTEGER},
      </if>
      <if test="gallery != null">
        #{gallery,jdbcType=VARCHAR},
      </if>
      <if test="keywords != null">
        #{keywords,jdbcType=VARCHAR},
      </if>
      <if test="brief != null">
        #{brief,jdbcType=VARCHAR},
      </if>
      <if test="isOnSale != null">
        #{isOnSale,jdbcType=BIT},
      </if>
      <if test="isSellOut != null">
        #{isSellOut,jdbcType=BIT},
      </if>
      <if test="sortOrder != null">
        #{sortOrder,jdbcType=SMALLINT},
      </if>
      <if test="picUrl != null">
        #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="shareUrl != null">
        #{shareUrl,jdbcType=VARCHAR},
      </if>
      <if test="isNew != null">
        #{isNew,jdbcType=BIT},
      </if>
      <if test="isHot != null">
        #{isHot,jdbcType=BIT},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="counterPrice != null">
        #{counterPrice,jdbcType=DECIMAL},
      </if>
      <if test="retailPrice != null">
        #{retailPrice,jdbcType=DECIMAL},
      </if>
      <if test="inventoryNum != null">
        #{inventoryNum,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="browse != null">
        #{browse,jdbcType=INTEGER},
      </if>
      <if test="sales != null">
        #{sales,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="commpany != null">
        #{commpany,jdbcType=VARCHAR},
      </if>
      <if test="wholesalePrice != null">
        #{wholesalePrice,jdbcType=DECIMAL},
      </if>
      <if test="approveStatus != null">
        #{approveStatus,jdbcType=TINYINT},
      </if>
      <if test="approveMsg != null">
        #{approveMsg,jdbcType=VARCHAR},
      </if>
      <if test="brokerageType != null">
        #{brokerageType,jdbcType=TINYINT},
      </if>
      <if test="brokeragePrice != null">
        #{brokeragePrice,jdbcType=DECIMAL},
      </if>
      <if test="saleRange != null">
        #{saleRange,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="transportType != null">
        #{transportType,jdbcType=INTEGER},
      </if>
      <if test="weigth != null">
        #{weigth,jdbcType=DECIMAL},
      </if>
      <if test="detail != null">
        #{detail,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.pioneer.mall.generate.domain.TpmGoodsExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from tpm_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_goods
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.goodsSn != null">
        goods_sn = #{record.goodsSn,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryId != null">
        category_id = #{record.categoryId,jdbcType=INTEGER},
      </if>
      <if test="record.shopId != null">
        shop_id = #{record.shopId,jdbcType=INTEGER},
      </if>
      <if test="record.brandId != null">
        brand_id = #{record.brandId,jdbcType=INTEGER},
      </if>
      <if test="record.gallery != null">
        gallery = #{record.gallery,jdbcType=VARCHAR},
      </if>
      <if test="record.keywords != null">
        keywords = #{record.keywords,jdbcType=VARCHAR},
      </if>
      <if test="record.brief != null">
        brief = #{record.brief,jdbcType=VARCHAR},
      </if>
      <if test="record.isOnSale != null">
        is_on_sale = #{record.isOnSale,jdbcType=BIT},
      </if>
      <if test="record.isSellOut != null">
        is_sell_out = #{record.isSellOut,jdbcType=BIT},
      </if>
      <if test="record.sortOrder != null">
        sort_order = #{record.sortOrder,jdbcType=SMALLINT},
      </if>
      <if test="record.picUrl != null">
        pic_url = #{record.picUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.shareUrl != null">
        share_url = #{record.shareUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.isNew != null">
        is_new = #{record.isNew,jdbcType=BIT},
      </if>
      <if test="record.isHot != null">
        is_hot = #{record.isHot,jdbcType=BIT},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.counterPrice != null">
        counter_price = #{record.counterPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.retailPrice != null">
        retail_price = #{record.retailPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.inventoryNum != null">
        inventory_num = #{record.inventoryNum,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.browse != null">
        `browse` = #{record.browse,jdbcType=INTEGER},
      </if>
      <if test="record.sales != null">
        sales = #{record.sales,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.commpany != null">
        commpany = #{record.commpany,jdbcType=VARCHAR},
      </if>
      <if test="record.wholesalePrice != null">
        wholesale_price = #{record.wholesalePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.approveStatus != null">
        approve_status = #{record.approveStatus,jdbcType=TINYINT},
      </if>
      <if test="record.approveMsg != null">
        approve_msg = #{record.approveMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.brokerageType != null">
        brokerage_type = #{record.brokerageType,jdbcType=TINYINT},
      </if>
      <if test="record.brokeragePrice != null">
        brokerage_price = #{record.brokeragePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.saleRange != null">
        sale_range = #{record.saleRange,jdbcType=INTEGER},
      </if>
      <if test="record.businessType != null">
        business_type = #{record.businessType,jdbcType=INTEGER},
      </if>
      <if test="record.transportType != null">
        transport_type = #{record.transportType,jdbcType=INTEGER},
      </if>
      <if test="record.weigth != null">
        weigth = #{record.weigth,jdbcType=DECIMAL},
      </if>
      <if test="record.detail != null">
        detail = #{record.detail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_goods
    set id = #{record.id,jdbcType=INTEGER},
      goods_sn = #{record.goodsSn,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      category_id = #{record.categoryId,jdbcType=INTEGER},
      shop_id = #{record.shopId,jdbcType=INTEGER},
      brand_id = #{record.brandId,jdbcType=INTEGER},
      gallery = #{record.gallery,jdbcType=VARCHAR},
      keywords = #{record.keywords,jdbcType=VARCHAR},
      brief = #{record.brief,jdbcType=VARCHAR},
      is_on_sale = #{record.isOnSale,jdbcType=BIT},
      is_sell_out = #{record.isSellOut,jdbcType=BIT},
      sort_order = #{record.sortOrder,jdbcType=SMALLINT},
      pic_url = #{record.picUrl,jdbcType=VARCHAR},
      share_url = #{record.shareUrl,jdbcType=VARCHAR},
      is_new = #{record.isNew,jdbcType=BIT},
      is_hot = #{record.isHot,jdbcType=BIT},
      unit = #{record.unit,jdbcType=VARCHAR},
      counter_price = #{record.counterPrice,jdbcType=DECIMAL},
      retail_price = #{record.retailPrice,jdbcType=DECIMAL},
      inventory_num = #{record.inventoryNum,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      `browse` = #{record.browse,jdbcType=INTEGER},
      sales = #{record.sales,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=BIT},
      commpany = #{record.commpany,jdbcType=VARCHAR},
      wholesale_price = #{record.wholesalePrice,jdbcType=DECIMAL},
      approve_status = #{record.approveStatus,jdbcType=TINYINT},
      approve_msg = #{record.approveMsg,jdbcType=VARCHAR},
      brokerage_type = #{record.brokerageType,jdbcType=TINYINT},
      brokerage_price = #{record.brokeragePrice,jdbcType=DECIMAL},
      sale_range = #{record.saleRange,jdbcType=INTEGER},
      business_type = #{record.businessType,jdbcType=INTEGER},
      transport_type = #{record.transportType,jdbcType=INTEGER},
      weigth = #{record.weigth,jdbcType=DECIMAL},
      detail = #{record.detail,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_goods
    set id = #{record.id,jdbcType=INTEGER},
      goods_sn = #{record.goodsSn,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      category_id = #{record.categoryId,jdbcType=INTEGER},
      shop_id = #{record.shopId,jdbcType=INTEGER},
      brand_id = #{record.brandId,jdbcType=INTEGER},
      gallery = #{record.gallery,jdbcType=VARCHAR},
      keywords = #{record.keywords,jdbcType=VARCHAR},
      brief = #{record.brief,jdbcType=VARCHAR},
      is_on_sale = #{record.isOnSale,jdbcType=BIT},
      is_sell_out = #{record.isSellOut,jdbcType=BIT},
      sort_order = #{record.sortOrder,jdbcType=SMALLINT},
      pic_url = #{record.picUrl,jdbcType=VARCHAR},
      share_url = #{record.shareUrl,jdbcType=VARCHAR},
      is_new = #{record.isNew,jdbcType=BIT},
      is_hot = #{record.isHot,jdbcType=BIT},
      unit = #{record.unit,jdbcType=VARCHAR},
      counter_price = #{record.counterPrice,jdbcType=DECIMAL},
      retail_price = #{record.retailPrice,jdbcType=DECIMAL},
      inventory_num = #{record.inventoryNum,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      `browse` = #{record.browse,jdbcType=INTEGER},
      sales = #{record.sales,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=BIT},
      commpany = #{record.commpany,jdbcType=VARCHAR},
      wholesale_price = #{record.wholesalePrice,jdbcType=DECIMAL},
      approve_status = #{record.approveStatus,jdbcType=TINYINT},
      approve_msg = #{record.approveMsg,jdbcType=VARCHAR},
      brokerage_type = #{record.brokerageType,jdbcType=TINYINT},
      brokerage_price = #{record.brokeragePrice,jdbcType=DECIMAL},
      sale_range = #{record.saleRange,jdbcType=INTEGER},
      business_type = #{record.businessType,jdbcType=INTEGER},
      transport_type = #{record.transportType,jdbcType=INTEGER},
      weigth = #{record.weigth,jdbcType=DECIMAL}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.pioneer.mall.generate.domain.TpmGoods">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_goods
    <set>
      <if test="goodsSn != null">
        goods_sn = #{goodsSn,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=INTEGER},
      </if>
      <if test="brandId != null">
        brand_id = #{brandId,jdbcType=INTEGER},
      </if>
      <if test="gallery != null">
        gallery = #{gallery,jdbcType=VARCHAR},
      </if>
      <if test="keywords != null">
        keywords = #{keywords,jdbcType=VARCHAR},
      </if>
      <if test="brief != null">
        brief = #{brief,jdbcType=VARCHAR},
      </if>
      <if test="isOnSale != null">
        is_on_sale = #{isOnSale,jdbcType=BIT},
      </if>
      <if test="isSellOut != null">
        is_sell_out = #{isSellOut,jdbcType=BIT},
      </if>
      <if test="sortOrder != null">
        sort_order = #{sortOrder,jdbcType=SMALLINT},
      </if>
      <if test="picUrl != null">
        pic_url = #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="shareUrl != null">
        share_url = #{shareUrl,jdbcType=VARCHAR},
      </if>
      <if test="isNew != null">
        is_new = #{isNew,jdbcType=BIT},
      </if>
      <if test="isHot != null">
        is_hot = #{isHot,jdbcType=BIT},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="counterPrice != null">
        counter_price = #{counterPrice,jdbcType=DECIMAL},
      </if>
      <if test="retailPrice != null">
        retail_price = #{retailPrice,jdbcType=DECIMAL},
      </if>
      <if test="inventoryNum != null">
        inventory_num = #{inventoryNum,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="browse != null">
        `browse` = #{browse,jdbcType=INTEGER},
      </if>
      <if test="sales != null">
        sales = #{sales,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="commpany != null">
        commpany = #{commpany,jdbcType=VARCHAR},
      </if>
      <if test="wholesalePrice != null">
        wholesale_price = #{wholesalePrice,jdbcType=DECIMAL},
      </if>
      <if test="approveStatus != null">
        approve_status = #{approveStatus,jdbcType=TINYINT},
      </if>
      <if test="approveMsg != null">
        approve_msg = #{approveMsg,jdbcType=VARCHAR},
      </if>
      <if test="brokerageType != null">
        brokerage_type = #{brokerageType,jdbcType=TINYINT},
      </if>
      <if test="brokeragePrice != null">
        brokerage_price = #{brokeragePrice,jdbcType=DECIMAL},
      </if>
      <if test="saleRange != null">
        sale_range = #{saleRange,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="transportType != null">
        transport_type = #{transportType,jdbcType=INTEGER},
      </if>
      <if test="weigth != null">
        weigth = #{weigth,jdbcType=DECIMAL},
      </if>
      <if test="detail != null">
        detail = #{detail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.pioneer.mall.generate.domain.TpmGoods">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_goods
    set goods_sn = #{goodsSn,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      category_id = #{categoryId,jdbcType=INTEGER},
      shop_id = #{shopId,jdbcType=INTEGER},
      brand_id = #{brandId,jdbcType=INTEGER},
      gallery = #{gallery,jdbcType=VARCHAR},
      keywords = #{keywords,jdbcType=VARCHAR},
      brief = #{brief,jdbcType=VARCHAR},
      is_on_sale = #{isOnSale,jdbcType=BIT},
      is_sell_out = #{isSellOut,jdbcType=BIT},
      sort_order = #{sortOrder,jdbcType=SMALLINT},
      pic_url = #{picUrl,jdbcType=VARCHAR},
      share_url = #{shareUrl,jdbcType=VARCHAR},
      is_new = #{isNew,jdbcType=BIT},
      is_hot = #{isHot,jdbcType=BIT},
      unit = #{unit,jdbcType=VARCHAR},
      counter_price = #{counterPrice,jdbcType=DECIMAL},
      retail_price = #{retailPrice,jdbcType=DECIMAL},
      inventory_num = #{inventoryNum,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      `browse` = #{browse,jdbcType=INTEGER},
      sales = #{sales,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=BIT},
      commpany = #{commpany,jdbcType=VARCHAR},
      wholesale_price = #{wholesalePrice,jdbcType=DECIMAL},
      approve_status = #{approveStatus,jdbcType=TINYINT},
      approve_msg = #{approveMsg,jdbcType=VARCHAR},
      brokerage_type = #{brokerageType,jdbcType=TINYINT},
      brokerage_price = #{brokeragePrice,jdbcType=DECIMAL},
      sale_range = #{saleRange,jdbcType=INTEGER},
      business_type = #{businessType,jdbcType=INTEGER},
      transport_type = #{transportType,jdbcType=INTEGER},
      weigth = #{weigth,jdbcType=DECIMAL},
      detail = #{detail,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pioneer.mall.generate.domain.TpmGoods">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_goods
    set goods_sn = #{goodsSn,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      category_id = #{categoryId,jdbcType=INTEGER},
      shop_id = #{shopId,jdbcType=INTEGER},
      brand_id = #{brandId,jdbcType=INTEGER},
      gallery = #{gallery,jdbcType=VARCHAR},
      keywords = #{keywords,jdbcType=VARCHAR},
      brief = #{brief,jdbcType=VARCHAR},
      is_on_sale = #{isOnSale,jdbcType=BIT},
      is_sell_out = #{isSellOut,jdbcType=BIT},
      sort_order = #{sortOrder,jdbcType=SMALLINT},
      pic_url = #{picUrl,jdbcType=VARCHAR},
      share_url = #{shareUrl,jdbcType=VARCHAR},
      is_new = #{isNew,jdbcType=BIT},
      is_hot = #{isHot,jdbcType=BIT},
      unit = #{unit,jdbcType=VARCHAR},
      counter_price = #{counterPrice,jdbcType=DECIMAL},
      retail_price = #{retailPrice,jdbcType=DECIMAL},
      inventory_num = #{inventoryNum,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      `browse` = #{browse,jdbcType=INTEGER},
      sales = #{sales,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=BIT},
      commpany = #{commpany,jdbcType=VARCHAR},
      wholesale_price = #{wholesalePrice,jdbcType=DECIMAL},
      approve_status = #{approveStatus,jdbcType=TINYINT},
      approve_msg = #{approveMsg,jdbcType=VARCHAR},
      brokerage_type = #{brokerageType,jdbcType=TINYINT},
      brokerage_price = #{brokeragePrice,jdbcType=DECIMAL},
      sale_range = #{saleRange,jdbcType=INTEGER},
      business_type = #{businessType,jdbcType=INTEGER},
      transport_type = #{transportType,jdbcType=INTEGER},
      weigth = #{weigth,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectOneByExample" parameterType="com.pioneer.mall.generate.domain.TpmGoodsExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <include refid="Base_Column_List" />
    from tpm_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleWithBLOBs" parameterType="com.pioneer.mall.generate.domain.TpmGoodsExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tpm_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, goods_sn, `name`, category_id, shop_id, brand_id, gallery, keywords, brief, is_on_sale, 
          is_sell_out, sort_order, pic_url, share_url, is_new, is_hot, unit, counter_price, 
          retail_price, inventory_num, add_time, update_time, `browse`, sales, deleted, commpany, 
          wholesale_price, approve_status, approve_msg, brokerage_type, brokerage_price, 
          sale_range, business_type, transport_type, weigth, detail
      </otherwise>
    </choose>
    from tpm_goods
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
  <update id="logicalDeleteByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_goods set deleted = 1
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_goods set deleted = 1
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>