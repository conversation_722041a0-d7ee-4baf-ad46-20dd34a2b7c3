<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.generate.dao.TpmCartMapper">
  <resultMap id="BaseResultMap" type="com.pioneer.mall.generate.domain.TpmCart">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="brand_id" jdbcType="INTEGER" property="brandId" />
    <result column="goods_id" jdbcType="INTEGER" property="goodsId" />
    <result column="goods_sn" jdbcType="VARCHAR" property="goodsSn" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="number" jdbcType="SMALLINT" property="number" />
    <result column="checked" jdbcType="BIT" property="checked" />
    <result column="pic_url" jdbcType="VARCHAR" property="picUrl" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="shop_id" jdbcType="INTEGER" property="shopId" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="settlement_money" jdbcType="DECIMAL" property="settlementMoney" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.pioneer.mall.generate.domain.TpmCart">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="specifications" jdbcType="LONGVARCHAR" property="specifications" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, user_id, brand_id, goods_id, goods_sn, goods_name, product_id, price, `number`, 
    `checked`, pic_url, business_type, shop_id, add_time, update_time, deleted, settlement_money
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    specifications
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.pioneer.mall.generate.domain.TpmCartExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tpm_cart
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.pioneer.mall.generate.domain.TpmCartExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tpm_cart
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <if test="example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, user_id, brand_id, goods_id, goods_sn, goods_name, product_id, price, `number`, 
          `checked`, pic_url, business_type, shop_id, add_time, update_time, deleted, settlement_money, 
          specifications
      </otherwise>
    </choose>
    from tpm_cart
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tpm_cart
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tpm_cart
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        1
      </when>
      <otherwise>
        0
      </otherwise>
    </choose>
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, user_id, brand_id, goods_id, goods_sn, goods_name, product_id, price, `number`, 
          `checked`, pic_url, business_type, shop_id, add_time, update_time, deleted, settlement_money, 
          specifications
      </otherwise>
    </choose>
    from tpm_cart
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_cart
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.pioneer.mall.generate.domain.TpmCartExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_cart
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.pioneer.mall.generate.domain.TpmCart">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_cart (user_id, brand_id, goods_id, 
      goods_sn, goods_name, product_id, 
      price, `number`, `checked`, 
      pic_url, business_type, shop_id, 
      add_time, update_time, deleted, 
      settlement_money, specifications)
    values (#{userId,jdbcType=INTEGER}, #{brandId,jdbcType=INTEGER}, #{goodsId,jdbcType=INTEGER}, 
      #{goodsSn,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, #{productId,jdbcType=INTEGER}, 
      #{price,jdbcType=DECIMAL}, #{number,jdbcType=SMALLINT}, #{checked,jdbcType=BIT}, 
      #{picUrl,jdbcType=VARCHAR}, #{businessType,jdbcType=INTEGER}, #{shopId,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT}, 
      #{settlementMoney,jdbcType=DECIMAL}, #{specifications,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.pioneer.mall.generate.domain.TpmCart">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_cart
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="brandId != null">
        brand_id,
      </if>
      <if test="goodsId != null">
        goods_id,
      </if>
      <if test="goodsSn != null">
        goods_sn,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="number != null">
        `number`,
      </if>
      <if test="checked != null">
        `checked`,
      </if>
      <if test="picUrl != null">
        pic_url,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="settlementMoney != null">
        settlement_money,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="goodsSn != null">
        #{goodsSn,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="number != null">
        #{number,jdbcType=SMALLINT},
      </if>
      <if test="checked != null">
        #{checked,jdbcType=BIT},
      </if>
      <if test="picUrl != null">
        #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="settlementMoney != null">
        #{settlementMoney,jdbcType=DECIMAL},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.pioneer.mall.generate.domain.TpmCartExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from tpm_cart
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_cart
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.brandId != null">
        brand_id = #{record.brandId,jdbcType=INTEGER},
      </if>
      <if test="record.goodsId != null">
        goods_id = #{record.goodsId,jdbcType=INTEGER},
      </if>
      <if test="record.goodsSn != null">
        goods_sn = #{record.goodsSn,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=INTEGER},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=DECIMAL},
      </if>
      <if test="record.number != null">
        `number` = #{record.number,jdbcType=SMALLINT},
      </if>
      <if test="record.checked != null">
        `checked` = #{record.checked,jdbcType=BIT},
      </if>
      <if test="record.picUrl != null">
        pic_url = #{record.picUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.businessType != null">
        business_type = #{record.businessType,jdbcType=INTEGER},
      </if>
      <if test="record.shopId != null">
        shop_id = #{record.shopId,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.settlementMoney != null">
        settlement_money = #{record.settlementMoney,jdbcType=DECIMAL},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_cart
    set id = #{record.id,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=INTEGER},
      brand_id = #{record.brandId,jdbcType=INTEGER},
      goods_id = #{record.goodsId,jdbcType=INTEGER},
      goods_sn = #{record.goodsSn,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      product_id = #{record.productId,jdbcType=INTEGER},
      price = #{record.price,jdbcType=DECIMAL},
      `number` = #{record.number,jdbcType=SMALLINT},
      `checked` = #{record.checked,jdbcType=BIT},
      pic_url = #{record.picUrl,jdbcType=VARCHAR},
      business_type = #{record.businessType,jdbcType=INTEGER},
      shop_id = #{record.shopId,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT},
      settlement_money = #{record.settlementMoney,jdbcType=DECIMAL},
      specifications = #{record.specifications,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_cart
    set id = #{record.id,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=INTEGER},
      brand_id = #{record.brandId,jdbcType=INTEGER},
      goods_id = #{record.goodsId,jdbcType=INTEGER},
      goods_sn = #{record.goodsSn,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      product_id = #{record.productId,jdbcType=INTEGER},
      price = #{record.price,jdbcType=DECIMAL},
      `number` = #{record.number,jdbcType=SMALLINT},
      `checked` = #{record.checked,jdbcType=BIT},
      pic_url = #{record.picUrl,jdbcType=VARCHAR},
      business_type = #{record.businessType,jdbcType=INTEGER},
      shop_id = #{record.shopId,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT},
      settlement_money = #{record.settlementMoney,jdbcType=DECIMAL}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.pioneer.mall.generate.domain.TpmCart">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_cart
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="brandId != null">
        brand_id = #{brandId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        goods_id = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="goodsSn != null">
        goods_sn = #{goodsSn,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="number != null">
        `number` = #{number,jdbcType=SMALLINT},
      </if>
      <if test="checked != null">
        `checked` = #{checked,jdbcType=BIT},
      </if>
      <if test="picUrl != null">
        pic_url = #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="settlementMoney != null">
        settlement_money = #{settlementMoney,jdbcType=DECIMAL},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.pioneer.mall.generate.domain.TpmCart">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_cart
    set user_id = #{userId,jdbcType=INTEGER},
      brand_id = #{brandId,jdbcType=INTEGER},
      goods_id = #{goodsId,jdbcType=INTEGER},
      goods_sn = #{goodsSn,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=INTEGER},
      price = #{price,jdbcType=DECIMAL},
      `number` = #{number,jdbcType=SMALLINT},
      `checked` = #{checked,jdbcType=BIT},
      pic_url = #{picUrl,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=INTEGER},
      shop_id = #{shopId,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT},
      settlement_money = #{settlementMoney,jdbcType=DECIMAL},
      specifications = #{specifications,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pioneer.mall.generate.domain.TpmCart">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_cart
    set user_id = #{userId,jdbcType=INTEGER},
      brand_id = #{brandId,jdbcType=INTEGER},
      goods_id = #{goodsId,jdbcType=INTEGER},
      goods_sn = #{goodsSn,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      product_id = #{productId,jdbcType=INTEGER},
      price = #{price,jdbcType=DECIMAL},
      `number` = #{number,jdbcType=SMALLINT},
      `checked` = #{checked,jdbcType=BIT},
      pic_url = #{picUrl,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=INTEGER},
      shop_id = #{shopId,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT},
      settlement_money = #{settlementMoney,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectOneByExample" parameterType="com.pioneer.mall.generate.domain.TpmCartExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <include refid="Base_Column_List" />
    from tpm_cart
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleWithBLOBs" parameterType="com.pioneer.mall.generate.domain.TpmCartExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tpm_cart
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length &gt; 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, user_id, brand_id, goods_id, goods_sn, goods_name, product_id, price, `number`, 
          `checked`, pic_url, business_type, shop_id, add_time, update_time, deleted, settlement_money, 
          specifications
      </otherwise>
    </choose>
    from tpm_cart
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
  <update id="logicalDeleteByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_cart set deleted = 1
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_cart set deleted = 1
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>