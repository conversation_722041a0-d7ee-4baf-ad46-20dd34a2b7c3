<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pioneer.mall.generate.dao.TpmCategoryMapper">
  <resultMap id="BaseResultMap" type="com.pioneer.mall.generate.domain.TpmCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="keywords" jdbcType="VARCHAR" property="keywords" />
    <result column="desc" jdbcType="VARCHAR" property="desc" />
    <result column="pid" jdbcType="INTEGER" property="pid" />
    <result column="icon_url" jdbcType="VARCHAR" property="iconUrl" />
    <result column="pic_url" jdbcType="VARCHAR" property="picUrl" />
    <result column="level" jdbcType="VARCHAR" property="level" />
    <result column="sort_order" jdbcType="INTEGER" property="sortOrder" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="shop_id" jdbcType="INTEGER" property="shopId" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, `name`, keywords, `desc`, pid, icon_url, pic_url, `level`, sort_order, business_type, 
    shop_id, add_time, update_time, deleted
  </sql>
  <select id="selectByExample" parameterType="com.pioneer.mall.generate.domain.TpmCategoryExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tpm_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <if test="example.distinct">
      distinct
    </if>
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, `name`, keywords, `desc`, pid, icon_url, pic_url, `level`, sort_order, business_type, 
          shop_id, add_time, update_time, deleted
      </otherwise>
    </choose>
    from tpm_category
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tpm_category
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPrimaryKeyWithLogicalDelete" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from tpm_category
    where id = #{id,jdbcType=INTEGER}
      and deleted = 
    <choose>
      <when test="andLogicalDeleted">
        1
      </when>
      <otherwise>
        0
      </otherwise>
    </choose>
  </select>
  <select id="selectByPrimaryKeySelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, `name`, keywords, `desc`, pid, icon_url, pic_url, `level`, sort_order, business_type, 
          shop_id, add_time, update_time, deleted
      </otherwise>
    </choose>
    from tpm_category
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_category
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.pioneer.mall.generate.domain.TpmCategoryExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from tpm_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.pioneer.mall.generate.domain.TpmCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_category (`name`, keywords, `desc`, 
      pid, icon_url, pic_url, 
      `level`, sort_order, business_type, 
      shop_id, add_time, update_time, 
      deleted)
    values (#{name,jdbcType=VARCHAR}, #{keywords,jdbcType=VARCHAR}, #{desc,jdbcType=VARCHAR}, 
      #{pid,jdbcType=INTEGER}, #{iconUrl,jdbcType=VARCHAR}, #{picUrl,jdbcType=VARCHAR}, 
      #{level,jdbcType=VARCHAR}, #{sortOrder,jdbcType=INTEGER}, #{businessType,jdbcType=INTEGER}, 
      #{shopId,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.pioneer.mall.generate.domain.TpmCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into tpm_category
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="keywords != null">
        keywords,
      </if>
      <if test="desc != null">
        `desc`,
      </if>
      <if test="pid != null">
        pid,
      </if>
      <if test="iconUrl != null">
        icon_url,
      </if>
      <if test="picUrl != null">
        pic_url,
      </if>
      <if test="level != null">
        `level`,
      </if>
      <if test="sortOrder != null">
        sort_order,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="keywords != null">
        #{keywords,jdbcType=VARCHAR},
      </if>
      <if test="desc != null">
        #{desc,jdbcType=VARCHAR},
      </if>
      <if test="pid != null">
        #{pid,jdbcType=INTEGER},
      </if>
      <if test="iconUrl != null">
        #{iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="picUrl != null">
        #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=VARCHAR},
      </if>
      <if test="sortOrder != null">
        #{sortOrder,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.pioneer.mall.generate.domain.TpmCategoryExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from tpm_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_category
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.keywords != null">
        keywords = #{record.keywords,jdbcType=VARCHAR},
      </if>
      <if test="record.desc != null">
        `desc` = #{record.desc,jdbcType=VARCHAR},
      </if>
      <if test="record.pid != null">
        pid = #{record.pid,jdbcType=INTEGER},
      </if>
      <if test="record.iconUrl != null">
        icon_url = #{record.iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.picUrl != null">
        pic_url = #{record.picUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.level != null">
        `level` = #{record.level,jdbcType=VARCHAR},
      </if>
      <if test="record.sortOrder != null">
        sort_order = #{record.sortOrder,jdbcType=INTEGER},
      </if>
      <if test="record.businessType != null">
        business_type = #{record.businessType,jdbcType=INTEGER},
      </if>
      <if test="record.shopId != null">
        shop_id = #{record.shopId,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_category
    set id = #{record.id,jdbcType=INTEGER},
      `name` = #{record.name,jdbcType=VARCHAR},
      keywords = #{record.keywords,jdbcType=VARCHAR},
      `desc` = #{record.desc,jdbcType=VARCHAR},
      pid = #{record.pid,jdbcType=INTEGER},
      icon_url = #{record.iconUrl,jdbcType=VARCHAR},
      pic_url = #{record.picUrl,jdbcType=VARCHAR},
      `level` = #{record.level,jdbcType=VARCHAR},
      sort_order = #{record.sortOrder,jdbcType=INTEGER},
      business_type = #{record.businessType,jdbcType=INTEGER},
      shop_id = #{record.shopId,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.pioneer.mall.generate.domain.TpmCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_category
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="keywords != null">
        keywords = #{keywords,jdbcType=VARCHAR},
      </if>
      <if test="desc != null">
        `desc` = #{desc,jdbcType=VARCHAR},
      </if>
      <if test="pid != null">
        pid = #{pid,jdbcType=INTEGER},
      </if>
      <if test="iconUrl != null">
        icon_url = #{iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="picUrl != null">
        pic_url = #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        `level` = #{level,jdbcType=VARCHAR},
      </if>
      <if test="sortOrder != null">
        sort_order = #{sortOrder,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pioneer.mall.generate.domain.TpmCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update tpm_category
    set `name` = #{name,jdbcType=VARCHAR},
      keywords = #{keywords,jdbcType=VARCHAR},
      `desc` = #{desc,jdbcType=VARCHAR},
      pid = #{pid,jdbcType=INTEGER},
      icon_url = #{iconUrl,jdbcType=VARCHAR},
      pic_url = #{picUrl,jdbcType=VARCHAR},
      `level` = #{level,jdbcType=VARCHAR},
      sort_order = #{sortOrder,jdbcType=INTEGER},
      business_type = #{businessType,jdbcType=INTEGER},
      shop_id = #{shopId,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectOneByExample" parameterType="com.pioneer.mall.generate.domain.TpmCategoryExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <include refid="Base_Column_List" />
    from tpm_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 1
  </select>
  <select id="selectOneByExampleSelective" parameterType="map" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    select
    <choose>
      <when test="selective != null and selective.length > 0">
        <foreach collection="selective" item="column" separator=",">
          ${column.escapedColumnName}
        </foreach>
      </when>
      <otherwise>
        id, `name`, keywords, `desc`, pid, icon_url, pic_url, `level`, sort_order, business_type, 
          shop_id, add_time, update_time, deleted
      </otherwise>
    </choose>
    from tpm_category
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
      order by ${example.orderByClause}
    </if>
    limit 1
  </select>
  <update id="logicalDeleteByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_category set deleted = 1
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="logicalDeleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      @project https://github.com/itfsw/mybatis-generator-plugin
    -->
    update tpm_category set deleted = 1
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>